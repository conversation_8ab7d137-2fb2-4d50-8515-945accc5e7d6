// 淘宝请求处理类
class TaobaoRequest {
    constructor() {
        this.initialized = false;
        this.init();
    }

    async init() {
        try {
            utils.log('init', '开始初始化淘宝助手...');
            this.initialized = true;
            utils.log('init', '初始化完成');
        } catch (e) {
            console.error('初始化失败:', e);
        }
    }

    // 获取token
    getTbToken() {
        const tokenMatch = document.cookie.match(/_tb_token_=([^;]+)/);
        return tokenMatch ? tokenMatch[1] : null;
    }

    // 获取商品状态
    async getItemStatus(itemId) {
        try {
            utils.log('status', '获取商品状态:', itemId);
            const response = await this.sendRequest('/auction/manager/table.htm', {
                itemId: itemId,
                type: 'json'
            });
            return response.data?.status || null;
        } catch (e) {
            utils.log('error', '获取商品状态失败:', e);
            throw e;
        }
    }

    // 更新商品状态
    async updateItemStatus(itemId, status) {
        try {
            utils.log('update', '更新商品状态:', {itemId, status});
            const action = status === 'onsale' ? 'puton' : 'putoff';
            await this.sendRequest('/auction/manager/updateStatus.htm', {
                itemId: itemId,
                action: action,
                type: 'json'
            }, 'POST');
            utils.log('update', '状态更新成功');
        } catch (e) {
            utils.log('error', '更新商品状态失败:', e);
            throw e;
        }
    }

    // 设置发货时间
    async setDeliveryTime(itemId, days) {
        try {
            utils.log('delivery', '设置发货时间:', {itemId, days});
            await this.sendRequest('/auction/manager/updateDeliveryTime.htm', {
                itemId: itemId,
                days: days,
                type: 'json'
            }, 'POST');
            utils.log('delivery', '发货时间设置成功');
        } catch (e) {
            utils.log('error', '设置发货时间失败:', e);
            throw e;
        }
    }

    // 发送请求
    async sendRequest(path, data = {}, method = 'GET') {
        const token = this.getTbToken();
        if (!token) {
            throw new Error('未获取到token');
        }

        // 构建请求参数
        const params = new URLSearchParams({
            ...data,
            _tb_token_: token,
            t: Date.now()
        });

        // 构建URL
        const baseUrl = 'https://qn.taobao.com';
        const url = method === 'GET' 
            ? `${baseUrl}${path}?${params.toString()}`
            : `${baseUrl}${path}`;

        // 发送请求
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: method === 'POST' ? params : undefined,
            credentials: 'include'
        });

        // 检查响应
        if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '操作失败');
        }

        return result;
    }

    // 上架商品
    async putOnSale(itemId) {
        utils.log('action', '开始上架商品:', itemId);
        return await this.updateItemStatus(itemId, 'onsale');
    }

    // 下架商品
    async putInStock(itemId) {
        utils.log('action', '开始下架商品:', itemId);
        return await this.updateItemStatus(itemId, 'instock');
    }
}

// 创建全局实例
window.taobaoRequest = new TaobaoRequest(); 