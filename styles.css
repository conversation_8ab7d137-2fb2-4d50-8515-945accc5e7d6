/* 控制面板容器 */
.tb-helper-container {
  position: fixed;
  right: 20px;
  top: 40%;
  transform: translateY(-50%);
  z-index: 999999999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid #FF4400;
  transition: all 0.3s ease;
}

.tb-helper-container:hover {
  transform: translateY(-50%) translateX(-5px);
  box-shadow: 5px 2px 15px rgba(0, 0, 0, 0.3);
}

/* 标题样式 */
.tb-helper-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  text-align: center;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

/* 按钮样式 */
.tb-helper-btn {
  min-width: 120px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #FF4400;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  text-align: center;
  margin: 5px 0;
  white-space: nowrap;
}

.tb-helper-btn:hover {
  background: #FF6633;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.tb-helper-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.tb-helper-btn-cancel {
  background: #999;
}

.tb-helper-btn-cancel:hover {
  background: #666;
}

/* 消息提示 */
.tb-helper-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 24px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10000000;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  max-width: 400px;
}

.tb-helper-message-close {
  margin-left: auto;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.tb-helper-message-close:hover {
  opacity: 1;
}

.tb-helper-icon {
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.tb-helper-icon-info {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>');
}

.tb-helper-icon-success {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2352c41a"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
}

.tb-helper-icon-error {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff4d4f"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/></svg>');
}

.tb-helper-message-info {
  border-left: 4px solid #1890ff;
}

.tb-helper-message-success {
  border-left: 4px solid #52c41a;
}

.tb-helper-message-error {
  border-left: 4px solid #ff4d4f;
}

.tb-helper-message-fade {
  opacity: 0;
  transform: translateX(100%);
}

/* 模态框 */
.tb-helper-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999999999;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.tb-helper-modal-fade {
  opacity: 0;
}

.tb-helper-modal-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  min-width: 300px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(1);
  transition: transform 0.3s ease;
}

.tb-helper-modal-fade .tb-helper-modal-content {
  transform: scale(0.8);
}

.tb-helper-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.tb-helper-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.tb-helper-modal-close {
  font-size: 20px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.tb-helper-modal-close:hover {
  color: #666;
}

.tb-helper-modal-body {
  margin-bottom: 24px;
}

.tb-helper-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 发货时间选择 */
.tb-helper-delivery-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tb-helper-delivery-options .tb-helper-btn {
  margin: 0;
}

.tb-helper-custom-days {
  display: flex;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.tb-helper-custom-days input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.tb-helper-custom-days input:focus {
  border-color: #FF4400;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tb-helper-container {
    right: 10px;
    padding: 10px;
  }
  
  .tb-helper-btn {
    min-width: 100px;
    padding: 8px 16px;
    font-size: 12px;
  }
  
  .tb-helper-message {
    max-width: 300px;
    font-size: 12px;
  }
}

/* 商品属性助手样式 */
.tb-attribute-btn {
  position: fixed;
  top: 100px;
  right: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  color: white;
  background-color: #ff4400;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  z-index: 9999;
  transition: background-color 0.3s;
}

.tb-attribute-btn:hover {
  background-color: #ff6633;
}

.tb-attribute-btn:active {
  background-color: #cc3300;
}

.tb-attribute-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  z-index: 10000;
  font-size: 14px;
  max-width: 80%;
  text-align: center;
  animation: fadeInOut 3s ease-in-out;
}

.tb-attribute-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
  z-index: 10000;
  max-width: 80%;
  max-height: 80vh;
  overflow-y: auto;
}

.tb-attribute-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.tb-attribute-panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.tb-attribute-panel-close {
  cursor: pointer;
  font-size: 18px;
  color: #999;
}

.tb-attribute-list {
  margin-bottom: 15px;
}

.tb-attribute-item {
  display: flex;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f5f5f5;
}

.tb-attribute-name {
  width: 120px;
  color: #666;
  font-size: 14px;
}

.tb-attribute-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.tb-attribute-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.tb-attribute-btn-secondary {
  padding: 6px 14px;
  font-size: 14px;
  border: 1px solid #ddd;
  background-color: white;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
}

.tb-attribute-btn-primary {
  padding: 6px 14px;
  font-size: 14px;
  background-color: #ff4400;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.tb-attribute-status {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.tb-attribute-status-item {
  display: flex;
  margin-bottom: 5px;
}

.tb-attribute-status-item.success {
  color: #52c41a;
}

.tb-attribute-status-item.error {
  color: #ff4d4f;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { opacity: 0; }
} 