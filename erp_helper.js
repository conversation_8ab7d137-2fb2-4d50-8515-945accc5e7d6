// ERP系统助手
class ERPHelper {
    constructor() {
        this.initialized = false;
        this.init();
        // 添加定时检查
        setInterval(() => this.checkAndAddButtons(), 1000);
    }

    async init() {
        try {
            // 等待表格加载完成
            await this.waitForGrid();
            // 开始监听表格变化
            this.observeGridChanges();
            this.initialized = true;
            console.log('ERP助手初始化完成');
        } catch (e) {
            console.error('ERP助手初始化失败:', e);
        }
    }

    // 等待表格加载
    async waitForGrid() {
        return new Promise((resolve, reject) => {
            const checkGrid = () => {
                const rows = document.querySelectorAll('.mini-grid-row');
                if (rows.length > 0) {
                    this.addButtonsToRows(rows);
                    resolve();
                    return;
                }
                setTimeout(checkGrid, 500);
            };
            checkGrid();
        });
    }

    // 监听表格变化
    observeGridChanges() {
        // 监听整个文档的变化
        const observer = new MutationObserver((mutations) => {
            this.checkAndAddButtons();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 监听鼠标点击事件
        document.addEventListener('click', () => {
            setTimeout(() => this.checkAndAddButtons(), 500);
        });

        // 监听分页按钮点击
        const pagerButtons = document.querySelectorAll('.mini-pager-index');
        pagerButtons.forEach(button => {
            button.addEventListener('click', () => {
                setTimeout(() => this.checkAndAddButtons(), 500);
            });
        });
    }

    // 检查并添加按钮
    checkAndAddButtons() {
        const rows = document.querySelectorAll('.mini-grid-row');
        if (rows.length > 0) {
            this.addButtonsToRows(rows);
        }
    }

    // 为每一行添加按钮
    addButtonsToRows(rows) {
        rows.forEach(row => {
            // 检查是否已添加按钮
            if (row.querySelector('.erp-helper-btn')) return;

            // 获取商品ID单元格
            const cells = row.querySelectorAll('td');
            // 遍历单元格找到包含商品ID的那个
            let idCell = null;
            cells.forEach(cell => {
                const cellText = cell.textContent.trim();
                // 检查是否是数字且长度大于10（淘宝商品ID通常很长）
                if (/^\d{10,}$/.test(cellText)) {
                    idCell = cell;
                }
            });

            if (!idCell) {
                return;
            }

            // 获取商品ID
            const itemId = idCell.textContent.trim();
            if (!itemId) {
                return;
            }

            // 创建按钮容器
            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'erp-helper-buttons';
            buttonContainer.style.cssText = 'display: inline-block; margin-left: 10px;';

            // 创建下架按钮
            const putoffButton = this.createButton('下架', () => this.handlePutoff(itemId));
            putoffButton.style.backgroundColor = '#FF5722';  // 设置下架按钮为红色
            buttonContainer.appendChild(putoffButton);

            // 创建发货时间按钮
            const deliveryButton = this.createButton('发货', () => this.handleDelivery(itemId));
            deliveryButton.style.backgroundColor = '#009688';  // 设置发货按钮为绿色
            buttonContainer.appendChild(deliveryButton);

            // 添加按钮到单元格
            const cellInner = idCell.querySelector('.mini-grid-cell-inner') || idCell;
            
            // 设置单元格样式
            cellInner.style.display = 'flex';
            cellInner.style.alignItems = 'center';
            cellInner.style.justifyContent = 'space-between';  // 商品ID和按钮分散对齐
            cellInner.style.width = '100%';  // 确保宽度充满
            
            // 创建商品ID容器
            const idContainer = document.createElement('div');
            idContainer.textContent = itemId;
            idContainer.style.marginRight = '10px';  // 添加一些间距
            
            // 清空原有内容并添加新的内容
            cellInner.innerHTML = '';
            cellInner.appendChild(idContainer);
            cellInner.appendChild(buttonContainer);
        });
    }

    // 创建按钮
    createButton(text, onClick) {
        const button = document.createElement('button');
        button.className = 'erp-helper-btn layui-btn layui-btn-small';
        button.style.cssText = 'font-size: 12px; margin-left: 5px; padding: 0 8px; height: 22px; line-height: 22px;';
        button.textContent = text;
        button.onclick = onClick;
        return button;
    }

    // 处理下架操作
    async handlePutoff(itemId) {
        try {
            if (!await utils.showConfirm('确认下架', '是否确认将该商品下架？')) {
                return;
            }

            // 打开新标签页执行下架操作
            const url = `https://qn.taobao.com/home.htm/SellManage/on_sale?queryItemId=${itemId}&action=putoff&current=1&pageSize=20`;
            await this.openBackgroundTab(url);

        } catch (e) {
            console.error('下架失败:', e);
            utils.showMessage(e.message || '下架失败，请重试', 'error');
        }
    }

    // 处理发货时间设置
    async handleDelivery(itemId) {
        try {
            const dialog = document.createElement('div');
            dialog.className = 'tb-helper-dialog';
            dialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 10000000;
            `;

            const options = [
                { text: '24小时内', value: 1, displayText: '24小时' },
                { text: '48小时内', value: 2, displayText: '48小时' },
                { text: '3天内', value: 3, displayText: '3天' },
                { text: '7天内', value: 7, displayText: '7天' },
                { text: '15天内', value: 15, displayText: '15天' }
            ];

            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = 'display: flex; gap: 10px; flex-wrap: wrap;';

            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 9999999;
            `;

            options.forEach(option => {
                const button = document.createElement('button');
                button.className = 'layui-btn layui-btn-small';
                button.textContent = option.text;
                button.onclick = async () => {
                    dialog.remove();
                    overlay.remove();

                    const url = `https://qn.taobao.com/home.htm/SellManage/on_sale?queryItemId=${itemId}&state=days:${option.value}&current=1&pageSize=20`;
                    await this.openBackgroundTab(url);
                };
                buttonContainer.appendChild(button);
            });

            dialog.appendChild(buttonContainer);
            overlay.onclick = () => {
                dialog.remove();
                overlay.remove();
            };

            document.body.appendChild(overlay);
            document.body.appendChild(dialog);

        } catch (e) {
            console.error('设置发货时间失败:', e);
            utils.showMessage(e.message || '操作失败，请重试', 'error');
        }
    }

    // 在后台打开标签页
    async openBackgroundTab(url) {
        try {
            // 创建临时链接
            const a = document.createElement('a');
            a.href = url;
            a.target = '_blank';
            a.rel = 'noopener noreferrer';
            a.style.display = 'none';
            document.body.appendChild(a);

            // 模拟Ctrl+点击
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                ctrlKey: true,  // Windows的Ctrl键
                metaKey: true   // Mac的Command键
            });

            // 触发点击事件
            a.dispatchEvent(clickEvent);

            // 清理临时元素
            setTimeout(() => {
                document.body.removeChild(a);
            }, 100);

            return Promise.resolve();
        } catch (e) {
            console.error('打开新标签页失败:', e);
            // 如果模拟点击失败，回退到原来的方式
            const newTab = window.open(url, '_blank', 'noopener,noreferrer');
            window.focus();
            return Promise.resolve(newTab);
        }
    }
}

// 创建实例
window.erpHelper = new ERPHelper(); 