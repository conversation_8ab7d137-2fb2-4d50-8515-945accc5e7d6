// 全局变量
let isModalOpen = false;

// 主要业务逻辑类
class TaobaoHelper {
    constructor() {
        this.initialized = false;
        // 立即开始初始化
        this.init();
        // 监听页面加载完成事件
        window.addEventListener('load', () => this.init());
        // 延迟2秒后再次尝试初始化
        setTimeout(() => this.init(), 2000);
    }

    // 初始化
    async init() {
        if (this.initialized) return;
        
        try {
            utils.log('init', '开始初始化淘宝助手...');
            
            // 检查是否是商品详情页
            if (!location.href.includes('item.htm')) {
                utils.log('init', '不是商品详情页，不初始化');
                return;
            }

            // 检查依赖是否加载
            if (!window.utils || !window.taobaoRequest) {
                utils.log('init', '依赖未加载完成');
                return;
            }

            // 检查商品信息
            const itemId = utils.getItemId();
            if (!itemId) {
                utils.log('init', '无法获取商品ID');
                return;
            }

            // 创建控制面板
            this.createControlPanel();
            
            this.initialized = true;
            utils.log('init', '淘宝助手初始化完成');
        } catch (e) {
            console.error('初始化失败:', e);
        }
    }

    // 获取商品ID
    getItemId() {
        try {
            return new URL(location.href).searchParams.get('id');
        } catch (e) {
            console.error('获取商品ID失败:', e);
            return null;
        }
    }

    // 发送API请求
    async sendRequest(url, data) {
        try {
            const token = document.cookie.match(/_tb_token_=([^;]+)/)?.[1];
            if (!token) {
                throw new Error('未获取到token');
            }

            const formData = new URLSearchParams({
                ...data,
                _tb_token_: token,
                t: Date.now()
            });

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData,
                credentials: 'include'
            });

            const result = await response.json();
            console.log('API响应:', result);
            
            if (!result.success) {
                throw new Error(result.message || '操作失败');
            }
            
            return result;
        } catch (e) {
            console.error('请求失败:', e);
            throw e;
        }
    }

    // 检查API可用性
    checkHubAPI() {
        console.log('检查Hub API可用性...');
        console.log('window.Hub:', window.Hub);
        
        if (!window.Hub) {
            console.log('Hub API不存在');
            return false;
        }

        // 检查必要的方法
        const requiredMethods = ['getStatus', 'updateStatus', 'setDeliveryTime'];
        const missingMethods = requiredMethods.filter(method => !window.Hub[method]);
        
        if (missingMethods.length > 0) {
            console.log('缺少必要的Hub API方法:', missingMethods);
            return false;
        }

        // 检查其他可能的API
        console.log('window.g_config:', window.g_config);
        console.log('window.TShop:', window.TShop);
        console.log('window.Shop:', window.Shop);
        console.log('window.Detail:', window.Detail);

        return true;
    }

    // 等待API加载
    async waitForAPI() {
        console.log('等待API加载...');
        for (let i = 0; i < 10; i++) {
            // 检查API可用性
            if (this.checkHubAPI()) {
                console.log('API加载完成');
                return true;
            }
            console.log(`第${i + 1}次等待...`);
            await new Promise(r => setTimeout(r, 500));
        }
        throw new Error('API加载超时');
    }

    // 获取商品状态
    async getItemStatus(itemId) {
        try {
            await this.waitForAPI();
            console.log('尝试获取商品状态:', itemId);

            // 尝试多种可能的方法
            let status;
            
            // 方法1: 直接使用Hub.getStatus
            try {
                status = await window.Hub.getStatus(itemId);
                console.log('通过Hub.getStatus获取状态成功:', status);
                return status;
            } catch (e) {
                console.log('Hub.getStatus失败:', e);
            }

            // 方法2: 通过Detail API
            try {
                status = window.Detail?.status;
                if (status) {
                    console.log('通过Detail.status获取状态成功:', status);
                    return status;
                }
            } catch (e) {
                console.log('Detail.status获取失败:', e);
            }

            // 方法3: 通过页面元素
            try {
                const statusElement = document.querySelector('[data-status]');
                if (statusElement) {
                    status = statusElement.dataset.status;
                    console.log('通过页面元素获取状态成功:', status);
                    return status;
                }
            } catch (e) {
                console.log('通过页面元素获取状态失败:', e);
            }

            throw new Error('无法获取商品状态');
        } catch (e) {
            console.error('获取商品状态失败:', e);
            throw e;
        }
    }

    // 更新商品状态
    async updateItemStatus(itemId, status) {
        try {
            await this.waitForAPI();
            console.log('尝试更新商品状态:', {itemId, status});

            // 尝试多种可能的方法
            // 方法1: Hub API
            try {
                await window.Hub.updateStatus(itemId, status);
                console.log('通过Hub.updateStatus更新成功');
                return;
            } catch (e) {
                console.log('Hub.updateStatus失败:', e);
            }

            // 方法2: Shop API
            try {
                if (window.Shop?.updateStatus) {
                    await window.Shop.updateStatus(itemId, status);
                    console.log('通过Shop.updateStatus更新成功');
                    return;
                }
            } catch (e) {
                console.log('Shop.updateStatus失败:', e);
            }

            throw new Error('更新商品状态失败');
        } catch (e) {
            console.error('更新商品状态失败:', e);
            throw e;
        }
    }

    // 设置发货时间
    async updateDeliveryTime(itemId, days) {
        try {
            await this.waitForAPI();
            console.log('使用Hub API设置发货时间');
            await window.Hub.setDeliveryTime(itemId, days);
        } catch (e) {
            console.error('设置发货时间失败:', e);
            throw e;
        }
    }

    // 处理上下架按钮点击
    async handleToggleClick() {
        try {
            utils.log('toggle', '上下架按钮被点击');
            const itemId = utils.getItemId();
            utils.log('toggle', '获取到商品ID:', itemId);
            
            if (!itemId) {
                utils.showMessage('无法获取商品ID', 'error');
                return;
            }

            // 获取当前商品状态
            utils.showMessage('正在获取商品状态...', 'info');
            const status = await window.taobaoRequest.getItemStatus(itemId);
            utils.log('toggle', '当前商品状态:', status);

            const isOnSale = status === 'onsale';
            const action = isOnSale ? '下架' : '上架';
            const newStatus = isOnSale ? 'instock' : 'onsale';

            // 显示确认对话框
            const confirmed = await utils.showConfirm(
                `确认${action}商品`,
                `是否确认将该商品${action}？`
            );

            if (!confirmed) {
                utils.log('toggle', '用户取消操作');
                return;
            }

            // 执行上下架操作
            utils.showMessage(`正在${action}商品...`, 'info');
            await window.taobaoRequest.updateItemStatus(itemId, newStatus);
            
            utils.showMessage(`商品${action}成功`, 'success');
            setTimeout(() => location.reload(), 1500);

        } catch (e) {
            console.error('上下架操作失败:', e);
            utils.showMessage(e.message || '操作失败，请重试', 'error');
        }
    }

    // 处理发货时间按钮点击
    async handleDeliveryClick() {
        try {
            utils.log('delivery', '发货时间按钮被点击');
            const itemId = utils.getItemId();
            utils.log('delivery', '获取到商品ID:', itemId);
            
            if (!itemId) {
                utils.showMessage('无法获取商品ID', 'error');
                return;
            }

            // 显示发货时间选择对话框
            const days = await utils.showDeliveryDialog();
            if (!days) {
                utils.log('delivery', '用户取消操作');
                return;
            }

            // 设置发货时间
            utils.showMessage('正在设置发货时间...', 'info');
            await window.taobaoRequest.setDeliveryTime(itemId, days);
            
            utils.showMessage('发货时间设置成功', 'success');
            setTimeout(() => location.reload(), 1500);

        } catch (e) {
            console.error('设置发货时间失败:', e);
            utils.showMessage(e.message || '操作失败，请重试', 'error');
        }
    }

    // 创建控制面板
    createControlPanel() {
        try {
            utils.log('panel', '开始创建控制面板...');
            
            // 移除已存在的面板
            const existingPanel = document.querySelector('.tb-helper-container');
            if (existingPanel) {
                existingPanel.remove();
            }

            // 创建新面板
            const container = document.createElement('div');
            container.className = 'tb-helper-container';
            
            // 创建标题
            const title = document.createElement('div');
            title.className = 'tb-helper-title';
            title.textContent = '商品管理';
            container.appendChild(title);
            
            // 创建上下架按钮
            const toggleButton = document.createElement('button');
            toggleButton.className = 'tb-helper-btn';
            toggleButton.textContent = '商品上下架';
            toggleButton.onclick = () => this.handleToggleClick();
            
            // 创建发货时间按钮
            const deliveryButton = document.createElement('button');
            deliveryButton.className = 'tb-helper-btn';
            deliveryButton.textContent = '设置发货时间';
            deliveryButton.onclick = () => this.handleDeliveryClick();
            
            // 添加按钮到容器
            container.appendChild(toggleButton);
            container.appendChild(deliveryButton);

            // 添加到页面
            document.body.appendChild(container);
            utils.log('panel', '控制面板创建完成');
        } catch (e) {
            console.error('创建控制面板失败:', e);
        }
    }
}

// 创建实例
utils.log('main', '创建TaobaoHelper实例...');
window.taobaoHelper = new TaobaoHelper();

// 移除旧的init函数和事件监听器
if (typeof init === 'function') {
    window.removeEventListener('load', init);
} 