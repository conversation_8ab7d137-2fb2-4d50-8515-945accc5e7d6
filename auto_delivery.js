// 确保utils对象存在
if (typeof utils === 'undefined') {
    console.error('utils对象未定义，请确保utils.js已加载');
    throw new Error('依赖缺失');
}

// 自动操作脚本
async function initAutoDelivery() {
    try {
        // 从URL获取参数
        const urlParams = new URLSearchParams(window.location.search);
        const itemId = urlParams.get('queryItemId');
        const action = urlParams.get('action');
        const state = urlParams.get('state');

        if (!itemId) return;

        // 等待页面加载完成
        await waitForTableLoad();

        // 查找商品行
        const foundRow = await findProductRow(itemId);
        if (!foundRow) {
            throw new Error('未找到商品');
        }

        // 如果是查看销量
        if (!action && !state) {
            // 等待销量数据加载
            await utils.sleep(500);  // 减少等待时间到500ms
            
            // 获取销量数据
            const totalSalesEl = foundRow.querySelector('div.qndatafont_md[style*="font-weight: 500"]');
            const sales30dEl = foundRow.querySelector('.text-async-label.qndatafont_md');
            
            // 如果没有找到元素，再等待500ms重试一次
            if (!totalSalesEl || !sales30dEl) {
                await utils.sleep(500);
                totalSalesEl = foundRow.querySelector('div.qndatafont_md[style*="font-weight: 500"]');
                sales30dEl = foundRow.querySelector('.text-async-label.qndatafont_md');
            }
            
            const totalSales = totalSalesEl?.textContent?.trim() || '0';
            const sales30d = sales30dEl?.textContent?.trim() || '0';
            
            // 发送销量数据给原页面
            window.opener?.postMessage({
                type: 'salesData',
                data: { totalSales, sales30d }
            }, '*');
            
            // 立即关闭页面
            window.close();
            return;
        }

        // 如果是下架操作
        if (action === 'putoff') {
            await executePutoffOperation(foundRow);
        }
        // 如果是设置发货时间
        else if (state) {
            const days = parseInt(state.split(':')[1]);
            if (!isNaN(days)) {
                await executeDeliveryTimeOperation(foundRow, days);
            }
        }
    } catch (e) {
        console.error('自动操作失败:', e);
        window.opener?.postMessage({ 
            type: 'error', 
            error: e.message 
        }, '*');
    }
}

// 执行下架操作
async function executePutoffOperation(row) {
    try {
        // 1. 选中商品
        const checkbox = row.querySelector('input[type="checkbox"], .next-checkbox');
        if (!checkbox) throw new Error('未找到复选框');
        checkbox.click();

        // 2. 点击下架按钮
        const putoffButton = Array.from(document.querySelectorAll('button'))
            .find(btn => btn.querySelector('.next-btn-helper')?.textContent.trim() === '批量下架');
        if (!putoffButton) throw new Error('未找到下架按钮');
        putoffButton.click();

        // 3. 确认下架
        const confirmDialog = await waitForElement('.next-dialog-wrapper .next-dialog', 3000);
        if (!confirmDialog) throw new Error('未找到确认对话框');
        
        const confirmBtn = confirmDialog.querySelector('.next-btn-primary');
        if (!confirmBtn) throw new Error('未找到确认按钮');
        confirmBtn.click();

        // 4. 等待操作结果
        await waitForSuccessMessage();
        
        // 5. 发送成功消息并关闭
        window.opener?.postMessage({ 
            type: 'success',
            message: '下架成功'
        }, '*');
        window.close();
    } catch (error) {
        window.opener?.postMessage({ 
            type: 'error', 
            error: error.message 
        }, '*');
        throw error;
    }
}

// 等待时间配置（可根据实际情况调整）
const WAIT_TIMES = {
    CHECKBOX_DELAY: 50,     // 选中商品后等待时间
    MENU_LOAD: 1500,       // 菜单加载等待时间
    MENU_READY: 30,        // 菜单展开后等待时间
    DIALOG_LOAD: 1200,     // 对话框加载等待时间
    DIALOG_READY: 30,      // 对话框准备就绪等待时间
    SUCCESS_MESSAGE: 1000,  // 成功消息等待时间
    CHECK_INTERVAL: 10,     // 元素检查间隔时间
    TABLE_CHECK_RETRY: 30   // 表格加载重试次数
};

// 执行发货时间设置
async function executeDeliveryTimeOperation(row, days) {
    try {
        // 1. 选中商品
        const checkbox = row.querySelector('input[type="checkbox"], .next-checkbox');
        if (!checkbox) throw new Error('未找到复选框');
        checkbox.click();
        
        // 等待选中状态生效
        await new Promise(r => setTimeout(r, WAIT_TIMES.CHECKBOX_DELAY));

        // 2. 点击更多操作
        const moreButton = Array.from(document.querySelectorAll('button'))
            .find(btn => btn.textContent.includes('更多批量操作'));
        if (!moreButton) throw new Error('未找到更多操作按钮');
        moreButton.click();

        // 3. 选择发货时间选项
        const menu = await waitForElement('.next-overlay-wrapper.opened .next-menu', WAIT_TIMES.MENU_LOAD);
        if (!menu) throw new Error('未找到操作菜单');

        // 等待菜单完全展开
        await new Promise(r => setTimeout(r, WAIT_TIMES.MENU_READY));

        // 尝试多种方式查找发货时间按钮
        let deliveryOption = Array.from(menu.querySelectorAll('button'))
            .find(btn => {
                const text = btn.textContent || '';
                return text.includes('批量设置发货时间') || text.includes('发货时间');
            });

        if (!deliveryOption) {
            deliveryOption = menu.querySelector('.next-menu-item button');
        }

        if (!deliveryOption) throw new Error('未找到发货时间选项');
        deliveryOption.click();

        // 4. 设置发货时间
        const dialog = await waitForElement('.next-dialog[role="dialog"]', WAIT_TIMES.DIALOG_LOAD);
        if (!dialog) throw new Error('未找到设置对话框');

        // 等待对话框完全加载
        await new Promise(r => setTimeout(r, WAIT_TIMES.DIALOG_READY));

        // 选择合适的选项
        const radioWrappers = dialog.querySelectorAll('.next-radio-wrapper');
        let targetRadio = null;
        let displayText = '';

        if (days === 1) {
            targetRadio = Array.from(radioWrappers)
                .find(radio => radio.textContent.includes('24小时内'));
            displayText = '24小时';
        } else if (days === 2) {
            targetRadio = Array.from(radioWrappers)
                .find(radio => radio.textContent.includes('48小时内'));
            displayText = '48小时';
        } else if (days >= 3) {
            targetRadio = Array.from(radioWrappers)
                .find(radio => radio.textContent.includes('大于48小时'));
            
            // 根据不同天数设置显示文本
            if (days === 3) displayText = '3天';
            else if (days === 7) displayText = '7天';
            else if (days === 15) displayText = '15天';
        }

        if (!targetRadio) throw new Error('未找到对应的时间选项');
        targetRadio.click();

        // 如果是大于48小时，需要填写天数
        if (days >= 3) {
            const input = dialog.querySelector('input[type="text"], input:not([type]), .next-input input');
            if (!input) throw new Error('未找到天数输入框');

            input.value = days.toString();
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // 5. 确认设置
        const confirmBtn = dialog.querySelector('.next-btn-primary');
        if (!confirmBtn) throw new Error('未找到确认按钮');
        confirmBtn.click();

        // 6. 等待操作结果
        await waitForSuccessMessage(WAIT_TIMES.SUCCESS_MESSAGE);
        
        // 7. 发送成功消息给原页面，使用displayText
        window.opener?.postMessage({ 
            type: 'success',
            message: `设置${displayText}发货成功`
        }, '*');
        
        // 8. 关闭当前页面
        window.close();
    } catch (error) {
        window.opener?.postMessage({ 
            type: 'error', 
            error: error.message 
        }, '*');
        throw error;
    }
}

// 查找商品行
async function findProductRow(itemId) {
    const row = document.querySelector(`.next-table-row[data-id="${itemId}"], tr[data-id="${itemId}"]`);
    if (row) return row;

    const rows = document.querySelectorAll('.next-table-row, tr');
    for (const row of rows) {
        if (row.textContent.includes(itemId) || row.innerHTML.includes(itemId)) {
            return row;
        }
    }

    throw new Error('未找到商品');
}

// 等待元素出现
async function waitForElement(selector, timeout = WAIT_TIMES.MENU_LOAD) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
        const element = document.querySelector(selector);
        if (element) return element;
        await new Promise(resolve => setTimeout(resolve, WAIT_TIMES.CHECK_INTERVAL));
    }
    
    return null;
}

// 等待成功提示消息
async function waitForSuccessMessage(timeout = WAIT_TIMES.SUCCESS_MESSAGE) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
        const successMessage = document.querySelector('.next-message.next-message-success, .next-feedback.next-feedback-success, .next-message-notice-content');
        if (successMessage?.textContent.includes('成功')) {
            return true;
        }
        await new Promise(resolve => setTimeout(resolve, WAIT_TIMES.CHECK_INTERVAL));
    }
    
    throw new Error('操作超时');
}

// 等待表格加载
async function waitForTableLoad() {
    return new Promise((resolve, reject) => {
        const table = document.querySelector('.next-table-body');
        if (table?.querySelectorAll('tr, .next-table-row').length > 0) {
            resolve();
            return;
        }

        let retryCount = 0;
        const maxRetries = WAIT_TIMES.TABLE_CHECK_RETRY;
        
        function checkTable() {
            const table = document.querySelector('.next-table-body');
            if (table?.querySelectorAll('tr, .next-table-row').length > 0) {
                resolve();
                return;
            }

            if (++retryCount >= maxRetries) {
                reject(new Error('表格加载超时'));
                return;
            }

            setTimeout(checkTable, WAIT_TIMES.CHECK_INTERVAL);
        }

        checkTable();
    });
}

// 立即执行初始化
(async function() {
    try {
        // 检查URL
        if (!window.location.href.includes('qn.taobao.com/home.htm/SellManage/on_sale')) {
            return;
        }

        // 等待页面加载完成
        if (document.readyState !== 'complete') {
            await new Promise(resolve => window.addEventListener('load', resolve));
        }

        // 减少初始等待时间
        await new Promise(r => setTimeout(r, 300));

        // 确保utils加载
        if (typeof utils === 'undefined') {
            console.error('utils未加载');
            return;
        }

        // 确保原页面保持焦点
        if (window.opener) {
            window.opener.focus();
        }

        // 执行自动操作
        await initAutoDelivery();
        
    } catch (e) {
        console.error('初始化失败:', e);
        window.opener?.postMessage({ 
            type: 'error', 
            error: e.message 
        }, '*');
    }
})(); 