# 淘宝商品属性助手

## 功能介绍

这是一个Chrome浏览器扩展，用于在淘宝商品详情页提取商品属性，并在编辑页面自动填充这些属性，帮助您快速复制商品信息，大幅提高工作效率。

## 主要功能

1. **提取商品属性**：在淘宝/天猫商品详情页自动提取商品的所有属性信息
2. **自动填充**：在淘宝商品编辑页面一键填充之前提取的属性信息
3. **智能匹配**：支持精确匹配和模糊匹配，即使属性名称略有差异也能正确填充
4. **特殊字段处理**：专门处理材质成分等特殊格式的属性
5. **高级下拉菜单匹配**：增强的下拉菜单选项匹配算法，支持多种匹配策略
6. **问题诊断**：内置调试面板，帮助排查填充失败的原因

## 使用方法

### 安装扩展

1. 下载并解压扩展文件夹
2. 打开Chrome浏览器，进入扩展管理页面（输入chrome://extensions/）
3. 开启"开发者模式"（右上角开关）
4. 点击"加载已解压的扩展程序"，选择解压后的文件夹
5. 扩展安装成功后，浏览器右上角会出现扩展图标

### 提取商品属性

1. 打开任意淘宝/天猫商品详情页
2. 页面中会出现"提取商品属性"按钮
3. 点击按钮后，扩展会自动提取页面上的所有商品属性
4. 提取完成后会显示提取到的所有属性信息
5. 您可以点击"复制到剪贴板"将属性数据保存，或直接关闭面板

### 填充商品属性

1. 打开淘宝商品编辑页面
2. 页面顶部会出现"填充商品属性"按钮
3. 点击按钮后，扩展会读取之前提取的属性数据
4. 显示确认面板，您可以查看将要填充的属性
5. 点击"确认填充"，扩展会自动填充所有匹配的属性
6. 填充完成后会显示结果统计，包括成功和失败的属性列表

## 特性说明

- **多种页面适配**：支持各种版本的淘宝/天猫商品详情页
- **智能字段识别**：自动识别下拉菜单、输入框、材质成分等不同类型的表单元素
- **模糊匹配**：即使属性名称有细微差别，也能找到对应的表单字段
- **特殊属性处理**：
  - 材质成分：自动解析材质和百分比，分别填入对应区域
  - 尺码和颜色：自动处理多选项数据
  - 带单位的数值：自动提取数值部分
- **下拉菜单增强填充**：
  - 多种匹配策略：精确匹配、首部匹配、包含匹配、关键词匹配
  - 特殊情况处理：年份季节、年龄范围等特殊格式支持
  - 多重尝试：当常规点击失败时，尝试多种DOM操作方式
  - 实时调试：填充过程的每一步都有详细日志

## 调试功能

当您在填充属性时遇到问题，扩展会自动显示调试面板，帮助您排查具体原因：

1. 调试面板会显示在页面右上角
2. 面板中记录了每个属性的填充过程和状态
3. 对于下拉菜单，会显示所有可选项和匹配策略
4. 当填充失败时，会详细记录失败原因
5. 您可以根据这些信息有针对性地处理问题

## 注意事项

1. 如果某些属性无法成功填充，请检查属性名称是否匹配
2. 特殊格式的属性（如材质成分）可能需要手动微调
3. 首次使用时请授予剪贴板权限，以便正常复制数据
4. 对于一些特殊类目的商品，可能需要手动调整部分属性
5. 如果下拉菜单无法自动填充，可查看调试面板了解具体原因

## 版本历史

- **v1.1.0**：优化下拉菜单填充功能，增加调试面板
- **v1.0.0**：初始版本，实现基本的提取和填充功能

## 联系与反馈

如果您在使用过程中遇到任何问题，或有功能改进建议，欢迎联系我们。 