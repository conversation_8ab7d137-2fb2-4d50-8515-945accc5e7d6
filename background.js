// 简化版背景脚本
console.log('淘宝商品属性助手: 背景脚本已加载');

// 监听消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('背景脚本收到消息:', message);
  
  if (message.action === 'storeAttributes') {
    // 存储提取的商品属性到扩展存储空间
    console.log('正在存储商品属性:', message.attributes);
    
    try {
      chrome.storage.local.set({ 'tbAttributes': message.attributes }, () => {
        const error = chrome.runtime.lastError;
        if (error) {
          console.error('存储属性时出错:', error);
          sendResponse({ success: false, error: error.message });
        } else {
          console.log('商品属性存储成功');
          sendResponse({ success: true });
        }
      });
    } catch (error) {
      console.error('尝试存储属性时出现异常:', error);
      sendResponse({ success: false, error: error.message });
    }
    
    return true; // 异步响应需要返回true
  }
  
  if (message.action === 'getAttributes') {
    // 获取存储的商品属性
    console.log('正在获取存储的商品属性');
    
    try {
      chrome.storage.local.get('tbAttributes', (result) => {
        const error = chrome.runtime.lastError;
        if (error) {
          console.error('获取属性时出错:', error);
          sendResponse({ success: false, error: error.message });
        } else {
          const attributes = result.tbAttributes || {};
          console.log('成功获取到属性:', attributes);
          sendResponse({ success: true, attributes: attributes });
        }
      });
    } catch (error) {
      console.error('尝试获取属性时出现异常:', error);
      sendResponse({ success: false, error: error.message });
    }
    
    return true; // 异步响应需要返回true
  }
  
  // 添加调试辅助功能
  if (message.action === 'debug') {
    console.log('收到调试请求:', message.data);
    sendResponse({ success: true, message: '调试信息已记录' });
    return true;
  }
  
  // 未知消息类型
  console.warn('收到未处理的消息类型:', message);
  sendResponse({ success: false, error: '未知的消息类型' });
  return true;
}); 