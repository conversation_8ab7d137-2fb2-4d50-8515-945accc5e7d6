// 商品属性助手 2.0 - 提取和填充淘宝商品属性

// 自执行函数
(function() {
  // 获取utils对象，确保与原始扩展兼容
  let utils;
  if (typeof window.utils !== 'undefined') {
    utils = window.utils;
    console.log('成功获取到原始扩展的utils对象');
  } else {
    // 创建最小化的utils对象，以便在原始扩展不存在时也能工作
    utils = {
      showMessage: function(message, type) {
        console.log(`[${type}] ${message}`);
        showToast(message);
      },
      showConfirm: function(title, message) {
        return confirm(`${title}\n${message}`);
      }
    };
    console.log('创建了最小化的utils对象');
  }
  
  // 等待页面完全加载
  window.addEventListener('load', () => {
    // 初始化助手
    initTaobaoAttributeHelper();
  });

  // 初始化助手
  function initTaobaoAttributeHelper() {
    // 检测当前页面类型
    const currentUrl = window.location.href;
    
    // 商品详情页
    if (isProductDetailPage(currentUrl)) {
      console.log('商品属性助手: 检测到商品详情页');
      initDetailPageExtractor();
    }
    
    // 商品编辑页
    if (isProductEditPage(currentUrl)) {
      console.log('商品属性助手: 检测到商品编辑页');
      initEditPageFiller();
    }
  }

  // 判断是否为商品详情页
  function isProductDetailPage(url) {
    return url.includes('item.taobao.com/item.htm') || 
           url.includes('detail.tmall.com/item.htm');
  }

  // 判断是否为商品编辑页
  function isProductEditPage(url) {
    return url.includes('item.upload.taobao.com/sell/v2/publish') || 
           url.includes('item.manager.taobao.com') ||
           url.includes('item.taobao.com/item.htm') && url.includes('edit');
  }

  // ---------------------------------------------------
  // 商品详情页提取功能
  // ---------------------------------------------------
  
  // 初始化详情页提取器
  function initDetailPageExtractor() {
    // 创建提取按钮
    const extractButton = document.createElement('button');
    extractButton.className = 'tb-attribute-btn';
    extractButton.textContent = '提取属性';
    extractButton.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999999; background: #FF4400; color: white; padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;';
    extractButton.onclick = extractProductAttributes;
    
    // 添加按钮到页面
    document.body.appendChild(extractButton);
    console.log('商品属性助手: 已添加提取按钮');
    
    // 全局函数，方便调试
    window.extractAttributes = extractProductAttributes;
  }

  // 提取商品属性
  function extractProductAttributes() {
    // 显示正在提取的提示
    showToast('正在提取商品属性...');
    console.log('[提取调试] 开始提取商品属性');
    
    try {
      // 提取属性数据
      const attributes = {};
      let extractedCount = 0;
      
      // 尝试方法1：新版淘宝详情页
      console.log('[提取调试] 尝试方法1：新版淘宝详情页');
      const newAttributeDivs = document.querySelectorAll('.atwl-dsc, .attributes-list, .detail-attributes-list, .tb-detailParam, .tb-attributes');
      
      if (newAttributeDivs && newAttributeDivs.length > 0) {
        console.log('[提取调试] 找到属性容器:', newAttributeDivs.length, '个');
        
        newAttributeDivs.forEach(container => {
          // 尝试找到列表项
          const items = container.querySelectorAll('li, .detail-attributes-item, .tb-attributes-item');
          
          if (items && items.length > 0) {
            console.log('[提取调试] 找到属性项:', items.length, '个');
            
            items.forEach(item => {
              // 尝试不同的选择器组合找到键值对
              const key = item.querySelector('.key, .tb-property-type, .tb-attributes-key, .name, [data-spm="prop_key"]')?.textContent?.trim();
              const value = item.querySelector('.val, .tb-property-cont, .tb-attributes-value, .value, [data-spm="prop_value"]')?.textContent?.trim();
              
              if (key && value) {
                console.log(`[提取调试] 提取到属性: ${key} = ${value}`);
                attributes[key] = value;
                extractedCount++;
              } else {
                // 尝试从整个item的文本内容中分离键值对
                const text = item.textContent.trim();
                const separatorIndex = text.indexOf('：');
                
                if (separatorIndex > 0) {
                  const extractedKey = text.substring(0, separatorIndex).trim();
                  const extractedValue = text.substring(separatorIndex + 1).trim();
                  
                  if (extractedKey && extractedValue) {
                    console.log(`[提取调试] 通过文本分离提取到属性: ${extractedKey} = ${extractedValue}`);
                    attributes[extractedKey] = extractedValue;
                    extractedCount++;
                  }
                }
              }
            });
          } else {
            // 尝试表格形式
            const tableRows = container.querySelectorAll('tr');
            if (tableRows && tableRows.length > 0) {
              console.log('[提取调试] 找到表格行:', tableRows.length, '个');
              
              tableRows.forEach(row => {
                const cells = row.querySelectorAll('td, th');
            if (cells.length >= 2) {
                  const key = cells[0].textContent.trim();
              const value = cells[1].textContent.trim();
                  
                  if (key && value) {
                    console.log(`[提取调试] 从表格提取到属性: ${key} = ${value}`);
                    attributes[key] = value;
                    extractedCount++;
              }
            }
          });
            }
          }
        });
      }
      
      // 方法2：尝试查找特定结构的元素
      if (extractedCount === 0) {
        console.log('[提取调试] 尝试方法2：特定结构元素');
        
        // 常见的属性容器类名
        const containerSelectors = [
          '.de-feature', '.attributes', '.tm-tableAttr', 
          '.attributes-list', '.tb-properties-list', '.tb-prop',
          '.param-list', '.product-params', '.infoItem'
        ];
        
        for (const selector of containerSelectors) {
          const containers = document.querySelectorAll(selector);
          console.log(`[提取调试] 查找选择器 ${selector}:`, containers.length, '个结果');
          
          containers.forEach(container => {
            // 尝试从<dl><dt>...</dt><dd>...</dd></dl>结构中提取
            const dts = container.querySelectorAll('dt');
            const dds = container.querySelectorAll('dd');
            
            if (dts.length > 0 && dts.length === dds.length) {
              console.log('[提取调试] 找到dt/dd结构:', dts.length, '对');
              
              for (let i = 0; i < dts.length; i++) {
                const key = dts[i].textContent.trim();
                const value = dds[i].textContent.trim();
                
                if (key && value) {
                  console.log(`[提取调试] 从dt/dd提取到属性: ${key} = ${value}`);
                  attributes[key] = value;
                  extractedCount++;
                }
              }
            }
            
            // 尝试从span对中提取
            const spans = container.querySelectorAll('span');
            if (spans.length >= 2 && spans.length % 2 === 0) {
              console.log('[提取调试] 找到span对:', spans.length / 2, '对');
              
              for (let i = 0; i < spans.length; i += 2) {
                const key = spans[i].textContent.trim();
                const value = spans[i + 1].textContent.trim();
                
                if (key && value) {
                  console.log(`[提取调试] 从span对提取到属性: ${key} = ${value}`);
                  attributes[key] = value;
                  extractedCount++;
                }
              }
            }
          });
          
          if (extractedCount > 0) {
            break; // 如果已找到属性，停止尝试其他选择器
          }
        }
      }
      
      // 方法3：尝试从页面中提取JSON数据
      if (extractedCount === 0) {
        console.log('[提取调试] 尝试方法3：页面JSON数据');
        
        // 查找页面上的JSON数据
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
          const text = script.textContent;
          if (text.includes('"商品属性"') || text.includes('"props"') || text.includes('"attributes"')) {
            console.log('[提取调试] 找到可能包含属性的脚本');
            
            try {
              // 尝试提取JSON数据
              const jsonMatches = text.match(/\{[\s\S]*?\}/g);
              if (jsonMatches) {
                for (const jsonStr of jsonMatches) {
                  try {
                    const data = JSON.parse(jsonStr);
                    
                    // 检查常见的数据结构
                    const propsData = data.props || data.attributes || data.productAttributes || data.itemData || data.item || {};
                    
                    if (propsData && typeof propsData === 'object') {
                      console.log('[提取调试] 从JSON找到可能的属性数据');
                      
                      // 提取属性
                      Object.entries(propsData).forEach(([key, value]) => {
                        if (typeof value === 'string' && key !== 'id' && key !== 'url') {
                          console.log(`[提取调试] 从JSON提取到属性: ${key} = ${value}`);
                          attributes[key] = value;
                          extractedCount++;
                        }
                      });
                      
                      if (extractedCount > 0) {
                        break;
                      }
                    }
                  } catch (e) {
                    // 忽略无法解析的JSON
                  }
                }
              }
            } catch (e) {
              console.log('[提取调试] JSON解析出错:', e);
            }
          }
        }
      }
      
      // 方法4：检查页面上的所有脚本并尝试匹配模式
      if (extractedCount === 0) {
        console.log('[提取调试] 尝试方法4：遍历脚本查找数据');
        
        try {
          // 找到所有脚本标签
          const allScripts = document.querySelectorAll('script');
          console.log('[提取调试] 找到脚本标签数量:', allScripts.length);
          
          for (const script of allScripts) {
            const content = script.textContent || '';
            
            if (content.includes('TShop.Setup') || content.includes('Hub.config') || content.includes('onSibRequestSuccess')) {
              console.log('[提取调试] 找到可能包含产品数据的脚本');
              
              // 尝试提取可能包含属性的JSON字符串
              // 1. 尝试匹配 "attributes":{"key":"value",...} 模式
              const propMatches = content.match(/"(attributes|props|specifications|itemParams|params)":\s*(\{[^}]*\})/g);
              if (propMatches) {
                console.log('[提取调试] 找到属性数据匹配:', propMatches.length, '个');
                
                for (const match of propMatches) {
                  try {
                    // 提取JSON对象部分
                    const jsonStr = match.replace(/^"[^"]+":/, '');
                    const data = JSON.parse(jsonStr);
                    
                    if (data && typeof data === 'object') {
                      Object.entries(data).forEach(([key, value]) => {
                        if (typeof value === 'string') {
                          console.log(`[提取调试] 从脚本匹配提取到属性: ${key} = ${value}`);
                          attributes[key] = value;
                          extractedCount++;
                        }
                      });
                    }
                  } catch (e) {
                    console.log('[提取调试] 解析属性数据失败:', e.message);
                  }
                }
              }
              
              // 2. 尝试匹配独立的属性行，如 "品牌": "Nike",
              const lineMatches = content.match(/"([^"]+)":\s*"([^"]+)"/g);
              if (lineMatches) {
                console.log('[提取调试] 找到属性行匹配:', lineMatches.length, '个');
                
                for (const match of lineMatches) {
                  try {
                    const keyValueMatch = match.match(/"([^"]+)":\s*"([^"]+)"/);
                    if (keyValueMatch && keyValueMatch.length === 3) {
                      const key = keyValueMatch[1];
                      const value = keyValueMatch[2];
                      
                      // 忽略明显不是商品属性的键
                      if (!['id', 'url', 'href', 'src', 'class', 'style'].includes(key.toLowerCase())) {
                        console.log(`[提取调试] 从脚本行提取到属性: ${key} = ${value}`);
                        attributes[key] = value;
                        extractedCount++;
                      }
                    }
                  } catch (e) {
                    // 忽略解析错误
                  }
                }
              }
            }
            
            if (extractedCount > 0) {
              break; // 如果找到了属性，就不再继续查找
            }
          }
        } catch (e) {
          console.error('[提取调试] 遍历脚本出错:', e);
        }
      }
      
      // 方法5：直接从DOM中提取商品信息（针对最新版淘宝）
      if (extractedCount === 0) {
        console.log('[提取调试] 尝试方法5：直接从DOM提取商品信息');
        
        try {
          // 1. 提取货号（通常页面上会显示）
          const itemNumberSelectors = [
            '.tb-item-id', '.tb-item-no', '.J_itemId', '#J_itemId', 
            '[data-spm="pro_id"]', '.item-id', '.item-no', '.sku-id'
          ];
          
          for (const selector of itemNumberSelectors) {
            const element = document.querySelector(selector);
            if (element) {
              const text = element.textContent.trim();
              const match = text.match(/\d+/);
              if (match) {
                console.log('[提取调试] 找到货号:', match[0]);
                attributes['货号'] = match[0];
                extractedCount++;
                break;
              }
            }
          }
          
          // 2. 提取品牌
          const brandSelectors = [
            '.brand-name', '.tb-brand', '.brand', '[data-spm="brand"]', 
            '.J_BrandName', '#J_BrandName'
          ];
          
          for (const selector of brandSelectors) {
            const element = document.querySelector(selector);
            if (element) {
              const brand = element.textContent.trim();
              if (brand) {
                console.log('[提取调试] 找到品牌:', brand);
                attributes['品牌'] = brand;
                extractedCount++;
                break;
              }
            }
          }
          
          // 3. 提取商品标题，可能包含季节信息
          const titleElement = document.querySelector('.tb-main-title, .tb-detail-hd h1, .tb-title, .title');
          if (titleElement) {
            const title = titleElement.textContent.trim();
            console.log('[提取调试] 找到商品标题:', title);
            
            // 从标题中提取可能的季节信息
            if (title.includes('春') || title.includes('夏') || title.includes('秋') || title.includes('冬')) {
              let season = '';
              if (title.includes('春')) season = '春季';
              else if (title.includes('夏')) season = '夏季';
              else if (title.includes('秋')) season = '秋季';
              else if (title.includes('冬')) season = '冬季';
              
              // 提取年份
              const yearMatch = title.match(/(20\d{2})/);
              const year = yearMatch ? yearMatch[1] : new Date().getFullYear();
              
              attributes['上市年份季节'] = `${year}年${season}`;
              console.log('[提取调试] 从标题提取季节:', attributes['上市年份季节']);
              extractedCount++;
            }
          }
          
          // 4. 提取当前URL中的itemId作为货号
          if (!attributes['货号']) {
            const urlMatch = window.location.href.match(/id=(\d+)/);
            if (urlMatch && urlMatch[1]) {
              console.log('[提取调试] 从URL提取货号:', urlMatch[1]);
              attributes['货号'] = urlMatch[1];
              extractedCount++;
            }
          }
        } catch (e) {
          console.error('[提取调试] 直接提取商品信息出错:', e);
        }
      }
      
      // 方法6：手动创建一些基本属性（如果什么都没提取到）
      if (extractedCount === 0) {
        console.log('[提取调试] 尝试方法6：创建基本属性');
        
        try {
          // 1. 从URL中提取商品ID作为货号
          const itemId = window.location.href.match(/id=(\d+)/)?.[1] || 
                        window.location.href.match(/item\/(\d+)/)?.[1] || 
                        '';
          
          if (itemId) {
            console.log('[提取调试] 创建货号:', itemId);
            attributes['货号'] = itemId;
            extractedCount++;
          }
          
          // 2. 创建默认的上市年份季节
          const now = new Date();
          const year = now.getFullYear();
          let season = '';
          
          const month = now.getMonth() + 1;
          if (month >= 3 && month <= 5) season = '春季';
          else if (month >= 6 && month <= 8) season = '夏季';
          else if (month >= 9 && month <= 11) season = '秋季';
          else season = '冬季';
          
          const seasonStr = `${year}年${season}`;
          console.log('[提取调试] 创建上市年份季节:', seasonStr);
          attributes['上市年份季节'] = seasonStr;
          extractedCount++;
          
        } catch (e) {
          console.error('[提取调试] 创建基本属性出错:', e);
        }
      }
      
      // 方法7：针对随机类名的新版淘宝页面结构
      console.log('[提取调试] 尝试方法7：针对随机类名的新版淘宝结构');
      
      try {
        // 搜索关键类名 - 我们会查找包含特定关键字的类名，而不是完全匹配
        // 这样即使淘宝更改了前缀或后缀，我们仍然能找到这些元素
        const infoItemSelectors = [
          '[class*="infoItem"]',
          '[class*="info-item"]',
          '[class*="attrItem"]',
          '[class*="attr-item"]',
          '[class*="parameter-item"]',
          '[class*="param-item"]'
        ];
        
        // 1. 查找所有可能的属性项容器
        let infoItems = [];
        
        for (const selector of infoItemSelectors) {
          const items = document.querySelectorAll(selector);
          console.log(`[提取调试] 选择器 "${selector}" 找到 ${items.length} 个结果`);
          if (items.length > 0) {
            infoItems = Array.from(items);
            break;
          }
        }
        
        // 如果没有找到明确的属性项，尝试查找嵌套结构中的属性项
        if (infoItems.length === 0) {
          // 先找到可能的参数信息区域
          const paramSections = document.querySelectorAll(
            '[class*="parameter"], [class*="param"], [class*="attrs"], [class*="properties"], [class*="props"], [class*="tabDetailItem"]'
          );
          
          console.log(`[提取调试] 找到 ${paramSections.length} 个参数区域`);
          
          // 从参数区域中寻找属性项
          for (const section of paramSections) {
            // 查找带有子元素的div结构 - 这是常见的属性项模式
            const divPairs = section.querySelectorAll('div > div');
            
            if (divPairs.length >= 2 && divPairs.length % 2 === 0) {
              console.log(`[提取调试] 在参数区域中找到 ${divPairs.length} 个div，尝试配对处理`);
              
              // 按照成对的方式处理这些div
              for (let i = 0; i < divPairs.length; i += 2) {
                try {
                  const titleEl = divPairs[i];
                  const valueEl = divPairs[i + 1];
                  
                  const title = titleEl.textContent.trim();
                  const value = valueEl.textContent.trim();
                  
                  if (title && value) {
                    console.log(`[提取调试] 从div对中提取: ${title} = ${value}`);
                    attributes[title] = value;
                    extractedCount++;
                  }
                } catch (e) {
                  // 忽略单个项处理错误
                }
              }
            }
          }
        }
        
        // 2. 处理找到的属性项
        if (infoItems.length > 0) {
          console.log(`[提取调试] 找到 ${infoItems.length} 个属性项，开始提取`);
          
          for (const item of infoItems) {
            try {
              // 尝试不同的子元素选择器组合来找到属性名和值
              const titleSelectors = [
                '[class*="title"]', 
                '[class*="key"]', 
                '[class*="name"]',
                'dt', 
                'th'
              ];
              
              const valueSelectors = [
                '[class*="content"]', 
                '[class*="value"]', 
                '[class*="val"]',
                'dd', 
                'td'
              ];
              
              // 查找标题元素
              let titleEl = null;
              for (const selector of titleSelectors) {
                const el = item.querySelector(selector);
                if (el) {
                  titleEl = el;
                  break;
                }
              }
              
              // 查找值元素
              let valueEl = null;
              for (const selector of valueSelectors) {
                const el = item.querySelector(selector);
                if (el) {
                  valueEl = el;
                  break;
                }
              }
              
              // 如果没有找到明确的标题或值元素，但有两个子元素，假设第一个是标题，第二个是值
              if (!titleEl || !valueEl) {
                const children = Array.from(item.children);
                if (children.length === 2) {
                  titleEl = children[0];
                  valueEl = children[1];
                }
              }
              
              // 如果找到了标题和值元素，则提取文本
              if (titleEl && valueEl) {
                const title = titleEl.textContent.trim();
                const value = valueEl.textContent.trim();
                
                if (title && value) {
                  console.log(`[提取调试] 提取属性: ${title} = ${value}`);
                  attributes[title] = value;
                  extractedCount++;
                }
              }
            } catch (e) {
              console.log('[提取调试] 处理单个属性项出错:', e.message);
            }
          }
        }
        
        // 3. 针对特定的表格结构 - 专门处理用户提供的结构样例
        const specificClassInfoItems = document.querySelectorAll('[class*="infoItem"]');
        console.log(`[提取调试] 特定查询 "[class*='infoItem']" 找到 ${specificClassInfoItems.length} 个结果`);
        
        if (specificClassInfoItems.length > 0) {
          console.log('[提取调试] 找到特定类名结构的属性项，直接处理');
          
          specificClassInfoItems.forEach(item => {
            try {
              const titleEl = item.querySelector('[class*="infoItemTitle"]');
              const valueEl = item.querySelector('[class*="infoItemContent"]');
              
              if (titleEl && valueEl) {
                const title = titleEl.getAttribute('title') || titleEl.textContent.trim();
                const value = valueEl.getAttribute('title') || valueEl.textContent.trim();
                
                if (title && value) {
                  console.log(`[提取调试] 从特定结构提取: ${title} = ${value}`);
                  attributes[title] = value;
                  extractedCount++;
                }
              }
            } catch (e) {
              console.log('[提取调试] 处理特定结构项出错:', e.message);
            }
          });
        }
        
      } catch (e) {
        console.error('[提取调试] 处理新版淘宝结构出错:', e);
      }
      
      // 检查是否提取到属性
      console.log('[提取调试] 提取完成，共提取到属性数量:', extractedCount);
      
      if (Object.keys(attributes).length === 0) {
        console.log('[提取调试] 未找到任何属性');
        showToast('未找到商品属性，请联系开发者完善');
        return;
      }
      
      // 存储到扩展存储
      chrome.runtime.sendMessage({ 
        action: 'storeAttributes', 
        attributes: attributes 
      }, response => {
        if (response && response.success) {
          // 简化交互，只显示成功提示，不再弹出属性面板
          showToast(`已成功提取${Object.keys(attributes).length}项属性`);
          console.log('[提取调试] 属性存储成功:', attributes);
          
          // 复制到剪贴板，方便用户需要时使用
          try {
            navigator.clipboard.writeText(JSON.stringify(attributes))
              .then(() => console.log('[提取调试] 属性已复制到剪贴板'))
              .catch(() => console.log('[提取调试] 无法复制到剪贴板'));
          } catch (e) {
            console.log('[提取调试] 复制到剪贴板失败', e);
          }
        } else {
          console.log('[提取调试] 属性存储失败:', response);
          showToast('属性存储失败，请重试');
        }
      });
      
    } catch (error) {
      console.error('[提取调试] 提取商品属性失败:', error);
      showToast('提取失败：' + error.message);
    }
  }

  // ---------------------------------------------------
  // 商品编辑页填充功能
  // ---------------------------------------------------
  
  // 初始化编辑页填充器
  function initEditPageFiller() {
    // 创建填充按钮
    const fillButton = document.createElement('button');
    fillButton.className = 'tb-attribute-btn';
    fillButton.textContent = '填充属性';
    fillButton.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999999; background: #FF4400; color: white; padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;';
    fillButton.onclick = fillProductAttributes;
    
    // 添加按钮到页面
    document.body.appendChild(fillButton);
    console.log('商品属性助手: 已添加填充按钮');
    
    // 全局函数，方便调试
    window.fillAttributes = fillProductAttributes;
  }

  // 填充商品属性
  async function fillProductAttributes() {
    // 显示正在填充的提示
    showToast('正在获取属性数据...');
    console.log('[填充调试] 开始获取属性数据');
    
    // 从扩展存储中获取之前提取的属性
    chrome.runtime.sendMessage({ action: 'getAttributes' }, async response => {
      if (response && response.success && response.attributes) {
        const attributes = response.attributes;
        
        // 检查是否有属性数据
        if (Object.keys(attributes).length === 0) {
          console.log('[填充调试] 存储中没有属性数据');
          showToast('未找到属性数据，请先在商品详情页提取');
          return;
        }
        
        console.log('[填充调试] 从存储获取到属性数据:', attributes);
        
        // 过滤掉不需要填充的属性（品牌、尺码、颜色分类）
        const filteredAttributes = {};
        for (const [name, value] of Object.entries(attributes)) {
          if (name !== '品牌' && name !== '尺码' && !name.includes('颜色分类')) {
            filteredAttributes[name] = value;
          } else {
            console.log(`[填充调试] 跳过属性: ${name}`);
          }
        }
        
        console.log(`[填充调试] 过滤后需要填充的属性:`, filteredAttributes);
        showToast(`开始填充 ${Object.keys(filteredAttributes).length} 项属性...`);
        
        // 等待页面完全加载和渲染
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 直接执行填充
        const results = await executeAttributeFill(filteredAttributes);
        
        // 填充完成后简单提示结果
        if (results.failed.length > 0) {
          console.log('[填充调试] 填充结果 - 成功:', results.success.length, '项, 失败:', results.failed.length, '项');
          console.log('[填充调试] 失败项:', results.failed);
          
          showToast(`填充完成! 成功: ${results.success.length} 项, 失败: ${results.failed.length} 项。如遇问题，请在控制台输入 copyDebugInfo() 复制调试信息。`);
        } else {
          console.log('[填充调试] 填充结果 - 全部成功:', results.success.length, '项');
          showToast(`填充完成! 所有 ${results.success.length} 项属性填充成功!`);
        }
        
      } else {
        // 尝试从剪贴板读取
        console.log('[填充调试] 存储中没有数据，尝试从剪贴板读取');
        try {
          const clipboardText = await navigator.clipboard.readText();
          try {
            const attributes = JSON.parse(clipboardText);
            if (typeof attributes === 'object' && Object.keys(attributes).length > 0) {
              console.log('[填充调试] 从剪贴板获取到属性数据:', attributes);
              
              // 过滤掉不需要填充的属性
    const filteredAttributes = {};
    for (const [name, value] of Object.entries(attributes)) {
      if (name !== '品牌' && name !== '尺码' && !name.includes('颜色分类')) {
        filteredAttributes[name] = value;
                } else {
                  console.log(`[填充调试] 跳过属性: ${name}`);
      }
    }
    
              console.log(`[填充调试] 过滤后需要填充的属性:`, filteredAttributes);
              showToast(`开始填充 ${Object.keys(filteredAttributes).length} 项属性...`);
              
              // 等待页面完全加载和渲染
              await new Promise(resolve => setTimeout(resolve, 500));
              
              // 直接执行填充
      const results = await executeAttributeFill(filteredAttributes);
              
              // 填充完成后简单提示结果
              if (results.failed.length > 0) {
                console.log('[填充调试] 填充结果 - 成功:', results.success.length, '项, 失败:', results.failed.length, '项');
                console.log('[填充调试] 失败项:', results.failed);
                
                showToast(`填充完成! 成功: ${results.success.length} 项, 失败: ${results.failed.length} 项。如遇问题，请在控制台输入 copyDebugInfo() 复制调试信息。`);
              } else {
                console.log('[填充调试] 填充结果 - 全部成功:', results.success.length, '项');
                showToast(`填充完成! 所有 ${results.success.length} 项属性填充成功!`);
              }
            } else {
              console.log('[填充调试] 剪贴板内容不是有效的属性数据');
              showToast('剪贴板内容无效，请先在商品详情页提取属性');
            }
          } catch (e) {
            console.error('[填充调试] 解析剪贴板JSON出错:', e);
            showToast('剪贴板数据格式错误，请先在商品详情页提取属性');
          }
        } catch (e) {
          console.error('[填充调试] 读取剪贴板失败:', e);
          showToast('无法读取剪贴板，请确保已授予剪贴板权限');
        }
      }
    });
  }

  // 执行属性填充
  async function executeAttributeFill(attributes) {
    showToast('正在填充商品属性...');
    console.log('[填充调试] 开始填充属性:', attributes);
    
    const results = {
      success: [],
      failed: []
    };
    
    // 添加临时调试功能 - 用于复制调试信息
    window.copyDebugInfo = function() {
      try {
        const debugData = {
          url: window.location.href,
          time: new Date().toISOString(),
          userAgent: navigator.userAgent,
          failedItems: results.failed.map(item => {
            return {
              name: item.name,
              value: item.value,
              reason: item.reason
            };
          })
        };
        
        const debugText = JSON.stringify(debugData, null, 2);
        navigator.clipboard.writeText(debugText)
          .then(() => {
            showToast('调试信息已复制到剪贴板，请发送给开发者');
            console.log('调试信息已复制到剪贴板');
          })
          .catch(err => {
            showToast('复制调试信息失败：' + err.message);
            console.error('复制调试信息失败', err);
          });
      } catch (e) {
        console.error('准备调试信息时出错', e);
        showToast('准备调试信息时出错：' + e.message);
      }
    };
    
    // 首先尝试特殊处理货号字段
    const itemNo = attributes['货号'] || attributes['商品编号'];
    if (itemNo) {
      try {
        console.log('[填充调试] 优先处理货号:', itemNo);
        const success = await fillItemNumberDirectly(itemNo);
        if (success) {
          results.success.push({ name: '货号', value: itemNo });
          // 从属性中移除已处理的货号
          delete attributes['货号'];
          delete attributes['商品编号'];
        } else {
          results.failed.push({ name: '货号', value: itemNo, reason: '直接填充失败' });
        }
      } catch (e) {
        console.error('[填充调试] 货号处理出错:', e.message);
        results.failed.push({ name: '货号', value: itemNo, reason: e.message });
      }
    }
    
    // 遍历所有属性
    for (const [name, value] of Object.entries(attributes)) {
      try {
        console.log(`[填充调试] 处理属性: ${name} = ${value}`);
        
        // 跳过不需要处理的属性
        if (name === '品牌' || name === '尺码' || name.includes('颜色分类')) {
          console.log(`[填充调试] 跳过属性: ${name}`);
          continue;
        }
        
        // 查找对应的表单字段
        const fieldElement = findFormField(name);
        
        if (!fieldElement) {
          console.log(`[填充调试] 未找到字段: ${name}`);
          results.failed.push({ name, value, reason: '未找到对应字段' });
          continue;
        }
        
        // 根据字段类型填充值
        const fieldType = detectFieldType(fieldElement);
        
        // 添加延迟，确保上一个字段操作完成
        await new Promise(resolve => setTimeout(resolve, 300));
        
        console.log(`[填充调试] 开始填充: ${name}, 类型: ${fieldType}, 值: ${value}`);
        const fillResult = await fillField(fieldElement, fieldType, value);
        
        if (fillResult.success) {
          console.log(`[填充调试] 成功填充: ${name}`);
          results.success.push({ name, value });
        } else {
          console.log(`[填充调试] 填充失败: ${name}, 原因: ${fillResult.reason}`);
          results.failed.push({ name, value, reason: fillResult.reason });
        }
        
      } catch (error) {
        console.error(`[填充调试] 处理属性出错: ${name}`, error);
        results.failed.push({ name, value, reason: error.message });
      }
      
      // 间隔一小段时间，避免操作过快
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    console.log('[填充调试] 填充完成, 结果:', results);
    return results;
  }

  // 查找表单字段
  function findFormField(attributeName) {
    console.log(`[填充调试] 正在查找字段: ${attributeName}`);
    
    // 新版淘宝界面的标签选择器
    const newLabels = Array.from(document.querySelectorAll('.sell-component-info-wrapper-label'));
    
    // 尝试在新版界面中精确匹配
    const newExactMatch = newLabels.find(label => {
      const text = label.textContent.trim();
      return text === attributeName || text === attributeName + '*';
    });
    
    if (newExactMatch) {
      console.log(`[填充调试] 在新版界面找到精确匹配字段: ${attributeName}`);
      const fieldWrapper = newExactMatch.closest('.sell-component-info-wrapper-wrap') || 
                          newExactMatch.closest('.sell-component-item-prop-item');
      console.log('[填充调试] 找到字段元素:', fieldWrapper);
      return fieldWrapper;
    }
    
    // 尝试在新版界面中模糊匹配
    const newFuzzyMatch = newLabels.find(label => {
      const text = label.textContent.trim();
      return text.includes(attributeName) || attributeName.includes(text);
    });
    
    if (newFuzzyMatch) {
      console.log(`[填充调试] 在新版界面找到模糊匹配字段: ${attributeName}`);
      const fieldWrapper = newFuzzyMatch.closest('.sell-component-info-wrapper-wrap') || 
                          newFuzzyMatch.closest('.sell-component-item-prop-item');
      console.log('[填充调试] 找到字段元素:', fieldWrapper);
      return fieldWrapper;
    }
    
    // 回退到旧版界面选择器
    const oldLabels = Array.from(document.querySelectorAll('.label'));
    
    // 旧版界面精确匹配
    const oldExactMatch = oldLabels.find(label => {
      const text = label.textContent.trim();
      return text === attributeName || text === attributeName + '*';
    });
    
    if (oldExactMatch) {
      console.log(`[填充调试] 在旧版界面找到精确匹配字段: ${attributeName}`);
      const oldFieldWrapper = oldExactMatch.closest('.sell-catProp-item');
      console.log('[填充调试] 找到字段元素:', oldFieldWrapper);
      return oldFieldWrapper;
    }
    
    // 旧版界面模糊匹配
    const oldFuzzyMatch = oldLabels.find(label => {
      const text = label.textContent.trim();
      return text.includes(attributeName) || attributeName.includes(text);
    });
    
    if (oldFuzzyMatch) {
      console.log(`[填充调试] 在旧版界面找到模糊匹配字段: ${attributeName}`);
      const oldFieldWrapper = oldFuzzyMatch.closest('.sell-catProp-item');
      console.log('[填充调试] 找到字段元素:', oldFieldWrapper);
      return oldFieldWrapper;
    }
    
    // 第三种尝试：使用ID属性查找
    // 常见属性的ID映射
    const propertyIdMap = {
      '上市年份季节': 'p-122216347',
      '货号': 'p-13021751', 
      '品牌': 'p-20000',
      '材质': 'p-20021',
      '适用年龄': 'p-20017',
      '产地': 'p-21299',
      '吊牌价': 'p-344943689',
      '成分含量': 'p-13328588',
      '穿着方式': 'p-8508',
      '组合形式': 'p-2576403',
      '袖长': 'p-7001',
      '裤长': 'p-8520',
      '领型': 'p-20602'
    };
    
    // 检查是否有匹配的ID
    const propId = propertyIdMap[attributeName];
    if (propId) {
      console.log(`[填充调试] 尝试通过ID查找字段: ${propId}`);
      // 尝试多种可能的ID选择器
      const idField = document.querySelector(`#sell-field-${propId}`) || 
                      document.querySelector(`#struct-${propId}`);
      
      if (idField) {
        const fieldWrapper = idField.closest('.sell-component-info-wrapper-wrap') || 
                            idField.closest('.sell-component-item-prop-item') ||
                            idField;
        console.log('[填充调试] 通过ID找到字段元素:', fieldWrapper);
        return fieldWrapper;
      }
    }
    
    console.log(`[填充调试] 未找到字段: ${attributeName}`);
    return null;
  }

  // 检测字段类型
  function detectFieldType(fieldElement) {
    if (!fieldElement) {
      console.log('[填充调试] 无法检测字段类型：字段元素为空');
      return 'unknown';
    }
    
    // 检查是否是下拉菜单（新版界面）
    if (fieldElement.querySelector('.next-select')) {
      console.log('[填充调试] 检测到字段类型: select');
      return 'select';
    }
    
    // 检查是否是材质成分特殊字段
    if (fieldElement.querySelector('.sell-catProp-item-material') || 
        fieldElement.querySelector('.material-item')) {
      console.log('[填充调试] 检测到字段类型: material');
      return 'material';
    }
    
    // 检查是否是带单位的数值输入
    if (fieldElement.querySelector('.sell-o-measurement')) {
      console.log('[填充调试] 检测到字段类型: measurement');
      return 'measurement';
    }
    
    // 检查是否是普通输入框
    if (fieldElement.querySelector('input[type="text"]') || 
        fieldElement.querySelector('.next-input input') ||
        fieldElement.querySelector('input')) {
      console.log('[填充调试] 检测到字段类型: input');
      return 'input';
    }
    
    console.log('[填充调试] 未知字段类型，元素:', fieldElement);
    return 'unknown';
  }

  // 填充字段
  async function fillField(fieldElement, fieldType, value) {
    switch (fieldType) {
      case 'select':
        return await fillSelectField(fieldElement, value);
      
      case 'material':
        return await fillMaterialField(fieldElement, value);
      
      case 'measurement':
        return await fillMeasurementField(fieldElement, value);
      
      case 'input':
        return await fillInputField(fieldElement, value);
      
      default:
        return { success: false, reason: '不支持的字段类型' };
    }
  }

  // 填充下拉菜单 - 增强版搜索选择策略
  async function fillSelectField(fieldElement, value) {
    try {
      // 获取字段名称
      const fieldLabel = fieldElement.querySelector('.next-form-item-label, .next-col-fixed')?.textContent.trim() || '未知字段';
      console.log(`[填充调试] 尝试填充字段 "${fieldLabel}" 的下拉菜单值: "${value}"`);
      
      // 找到下拉菜单
      const select = fieldElement.querySelector('.next-select, [class*="-select"], [role="combobox"]');
      if (!select) {
        console.error(`[填充调试] 未找到 "${fieldLabel}" 的下拉菜单元素`);
        return { success: false, reason: '未找到下拉菜单元素' };
      }
      
      // 记录下拉菜单类型信息
      console.log(`[填充调试] 下拉菜单类型: ${select.className}`);
      
      // 检查当前是否已经选中了正确的值
      const currentValue = select.querySelector('.next-select-values em, .next-select-inner em') || 
                           select.querySelector('[class*="select-value"] em');
      
      const currentText = currentValue ? currentValue.textContent.trim() : '';
      if (currentText === value) {
        console.log(`[填充调试] "${fieldLabel}" 已经选中了正确的值: "${value}"`);
        return { success: true };
      }
      
      console.log(`[填充调试] "${fieldLabel}" 当前值: "${currentText}", 目标值: "${value}"`);
      
      // 清除当前值 (如果有)
      const clearButton = select.querySelector('.next-select-clear');
      if (clearButton && currentText) {
        console.log(`[填充调试] "${fieldLabel}" 清除当前值`);
        clearButton.click();
        await new Promise(resolve => setTimeout(resolve, 300));
      }
      
      // 1. 点击下拉菜单打开它
      console.log(`[填充调试] "${fieldLabel}" 点击下拉菜单`);
      select.click();
      
      // 等待下拉菜单弹出
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 2. 寻找并检查下拉菜单覆盖层 - 多方式检测
      let overlay = await findActiveOverlay();
      if (!overlay) {
        console.log(`[填充调试] "${fieldLabel}" 未找到下拉菜单覆盖层，尝试再次点击`);
        // 如果第一次没找到，再次尝试点击
        select.click();
        await new Promise(resolve => setTimeout(resolve, 1200));
        overlay = await findActiveOverlay();
      }
      
      if (!overlay) {
        console.error(`[填充调试] "${fieldLabel}" 无法找到下拉菜单覆盖层`);
        try { document.body.click(); } catch(e) {}
        return { success: false, reason: '无法找到下拉菜单覆盖层' };
      }
      
      console.log(`[填充调试] "${fieldLabel}" 找到下拉菜单覆盖层: ${overlay.className}`);
      
      // 3. 查找搜索框 - 处理各种可能的结构
      const searchInput = await findSearchInput(overlay);
      
      // 如果找到搜索框，使用搜索策略
      if (searchInput) {
        console.log(`[填充调试] "${fieldLabel}" 找到搜索输入框，开始搜索`);
        
        // 聚焦搜索框
        searchInput.focus();
        
        // 清空搜索框
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 精简搜索关键词 - 取最有特点的部分
        const searchTerm = getOptimalSearchTerm(value);
        console.log(`[填充调试] "${fieldLabel}" 在搜索框中输入: "${searchTerm}"`);
        
        // 输入搜索词
        searchInput.value = searchTerm;
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        searchInput.dispatchEvent(new Event('change', { bubbles: true }));
        
        // 等待搜索结果加载 - 增加等待时间以确保完全加载
        await new Promise(resolve => setTimeout(resolve, 1200));
        
        // 4. 再次获取覆盖层，因为搜索可能导致覆盖层刷新或重建
        const updatedOverlay = await findActiveOverlay();
        if (updatedOverlay) {
          // 5. 查找并点击搜索结果
          const success = await selectSearchResult(updatedOverlay, searchTerm, value, fieldLabel);
          if (success) {
            // 等待选择生效
            await new Promise(resolve => setTimeout(resolve, 600));
            return { success: true };
          }
        }
      }
      
      // 如果搜索策略失败或者没有搜索框，尝试直接选择策略
      console.log(`[填充调试] "${fieldLabel}" 搜索选择策略未成功，尝试直接选择策略`);
      return await directSelectionStrategy(overlay, value, select, fieldLabel);
      
    } catch (e) {
      console.error('[填充调试] 下拉菜单填充出错:', e.message);
      try { document.body.click(); } catch(e) {}
      return { success: false, reason: e.message };
    }
  }
  
  // 辅助函数：查找当前活动的下拉菜单覆盖层
  async function findActiveOverlay() {
    // 多种选择器，按优先级排序
    const selectors = [
      '.next-overlay-inner.next-select-popup-wrap',
      '.next-overlay-wrapper.opened .next-overlay-inner',
      '.next-overlay-inner.next-select-popup-wrap.sell-o-select-popup-overlay',
      '.sell-o-select-popup-overlay',
      '[class*="overlay"][style*="position: absolute"]',
      '[class*="popup"][style*="position: absolute"]',
      '[class*="select-popup-wrap"]'
    ];
    
    // 尝试所有选择器
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        // 找出可见的元素
        const visibleElement = Array.from(elements).find(el => 
          isElementVisible(el) && 
          el.getBoundingClientRect().width > 0 &&
          el.getBoundingClientRect().height > 0
        );
        
        if (visibleElement) return visibleElement;
      }
    }
    
    // 如果通过选择器没找到，尝试查找近期新增到DOM的元素
    const allOverlays = document.querySelectorAll('[class*="overlay"], [class*="popup"]');
    if (allOverlays.length > 0) {
      const visibleOverlays = Array.from(allOverlays).filter(el => 
        isElementVisible(el) && 
        el.getBoundingClientRect().width > 0 &&
        el.style.position === 'absolute' // 通常这些覆盖层都是绝对定位的
      );
      
      if (visibleOverlays.length > 0) {
        // 优先返回最靠近视口中心的覆盖层
        return findElementClosestToCenter(visibleOverlays);
      }
    }
    
    return null;
  }
  
  // 辅助函数：找出最接近视口中心的元素
  function findElementClosestToCenter(elements) {
    const viewportCenterX = window.innerWidth / 2;
    const viewportCenterY = window.innerHeight / 2;
    
    let closestElement = null;
    let closestDistance = Infinity;
    
    for (const element of elements) {
      const rect = element.getBoundingClientRect();
      const elementCenterX = rect.left + rect.width / 2;
      const elementCenterY = rect.top + rect.height / 2;
      
      const distance = Math.sqrt(
        Math.pow(elementCenterX - viewportCenterX, 2) +
        Math.pow(elementCenterY - viewportCenterY, 2)
      );
      
      if (distance < closestDistance) {
        closestDistance = distance;
        closestElement = element;
      }
    }
    
    return closestElement;
  }
  
  // 辅助函数：在覆盖层中查找搜索输入框
  async function findSearchInput(overlay) {
    // 直接尝试多种选择器
    const searchInput = 
      overlay.querySelector('.options-search input') ||
      overlay.querySelector('.next-input input') ||
      overlay.querySelector('[autocomplete="off"]') ||
      overlay.querySelector('input[type="text"]') ||
      overlay.querySelector('input');
    
    if (searchInput) {
      // 验证这真的是搜索框 (检查特征)
      const isSearchInput = 
        searchInput.getAttribute('autocomplete') === 'off' ||
        searchInput.closest('.options-search') ||
        searchInput.parentNode.className.includes('input-inner');
      
      if (isSearchInput) return searchInput;
    }
    
    // 如果常规选择器未找到搜索框，尝试查找具有动态ID的输入框
    const alternativeInputs = overlay.querySelectorAll('input[data-spm-anchor-id]');
    if (alternativeInputs.length > 0) {
      return alternativeInputs[0]; // 返回第一个具有此属性的输入框
    }
    
    return null;
  }
  
  // 辅助函数：从值中提取最优搜索词
  function getOptimalSearchTerm(value) {
    if (!value) return '';
    if (value.length <= 15) return value;
    
    // 对于长文本，尝试提取最有特点的部分
    // 1. 去除常见词
    const commonWords = ['的', '了', '和', '与', '或', '及', '等', '中', '式'];
    let terms = value.split(/[\s\+\-\/]/); // 按空格和常见分隔符拆分
    
    // 如果没有使用分隔符，尝试其他策略
    if (terms.length === 1) {
      // 尝试提取数字和英文
      const numericMatch = value.match(/\d+(\.\d+)?%?/);
      if (numericMatch) return numericMatch[0];
      
      const englishMatch = value.match(/[a-zA-Z]+/);
      if (englishMatch) return englishMatch[0];
      
      // 如果都没有，返回前10个字符
      return value.substring(0, 10);
    }
    
    // 过滤掉常见词和很短的词
    terms = terms.filter(term => 
      term.length > 1 && 
      !commonWords.includes(term)
    );
    
    // 如果过滤后没有词，返回原始值的前10个字符
    if (terms.length === 0) return value.substring(0, 10);
    
    // 返回最长的词 (通常最具特点)
    return terms.reduce((a, b) => a.length > b.length ? a : b);
  }
  
  // 辅助函数：选择搜索结果
  async function selectSearchResult(overlay, searchTerm, originalValue, fieldLabel) {
    console.log(`[填充调试] "${fieldLabel}" 尝试选择搜索结果`);
    
    // 1. 等待搜索结果稳定
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 2. 查找所有可能的选项容器
    const optionsContentContainer = overlay.querySelector('.options-content') ||
                                   overlay.querySelector('[class*="content"]');
    
    // 搜索结果应该在哪个容器中
    const container = optionsContentContainer || overlay;
    
    // 3. 查找所有选项 - 广泛的选择器范围
    let options = container.querySelectorAll('.options-item, [class*="options-item"], [class*="select-item"], div');
    
    // 过滤掉搜索框和无文本内容的元素
    const validOptions = Array.from(options).filter(option => {
      // 排除搜索框和其子元素
      if (option.querySelector('input') || option.closest('.options-search')) {
        return false;
      }
      
      // 排除空文本元素
      const text = option.textContent.trim();
      return text && text.length > 0;
    });
    
    console.log(`[填充调试] "${fieldLabel}" 搜索后找到的有效选项数量: ${validOptions.length}`);
    
    if (validOptions.length === 0) {
      console.log(`[填充调试] "${fieldLabel}" 搜索后未找到有效选项，可能没有匹配结果`);
      return false;
    }
    
    // 4. 评分和匹配
    let bestMatch = null;
    let bestScore = 0;
    
    for (let i = 0; i < validOptions.length; i++) {
      const option = validOptions[i];
      // 获取选项文本 - 尝试多种方法
      let optionText = '';
      
      // 从title属性获取
      if (option.getAttribute('title')) {
        optionText = option.getAttribute('title').trim();
      }
      // 从info-content获取
      else if (option.querySelector('.info-content')) {
        optionText = option.querySelector('.info-content').textContent.trim();
      }
      // 直接从文本内容获取
      else {
        optionText = option.textContent.trim();
      }
      
      // 忽略空或只有空白的文本
      if (!optionText) continue;
      
      // 计算匹配分数
      let score = 0;
      
      // 完全匹配
      if (optionText === originalValue) {
        score = 100;
      }
      // 包含原始值
      else if (optionText.includes(originalValue) || originalValue.includes(optionText)) {
        score = 80;
      }
      // 包含搜索词
      else if (optionText.includes(searchTerm) || searchTerm.includes(optionText)) {
        score = 60;
      }
      // 第一个选项作为兜底
      else if (i === 0) {
        score = 10;
      }
      
      // 记录得分最高的选项
      if (score > bestScore) {
        bestScore = score;
        bestMatch = option;
        console.log(`[填充调试] "${fieldLabel}" 找到匹配选项: "${optionText}" (得分: ${score})`);
      }
    }
    
    // 如果没有得分大于 0 的选项，选择第一个作为默认
    if (!bestMatch && validOptions.length > 0) {
      bestMatch = validOptions[0];
      const defaultText = bestMatch.textContent.trim();
      console.log(`[填充调试] "${fieldLabel}" 未找到匹配选项，使用默认: "${defaultText}"`);
    }
    
    if (!bestMatch) {
      console.log(`[填充调试] "${fieldLabel}" 无法确定要选择的选项`);
      return false;
    }
    
    // 5. 确保选项可见
    if (bestMatch.scrollIntoView) {
      bestMatch.scrollIntoView({ block: 'center' });
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    // 6. 多次尝试点击选项
    console.log(`[填充调试] "${fieldLabel}" 点击最佳选项`);
    
    // 方法1：直接点击
    bestMatch.click();
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // 检查下拉框是否已关闭 (选择成功的标志)
    if (!isElementVisible(overlay)) {
      console.log(`[填充调试] "${fieldLabel}" 选项点击成功，下拉框已关闭`);
      return true;
    }
    
    // 方法2：模拟鼠标事件
    console.log(`[填充调试] "${fieldLabel}" 尝试方法2: 模拟鼠标事件`);
    try {
      ['mousedown', 'mouseup', 'click'].forEach(eventType => {
        bestMatch.dispatchEvent(new MouseEvent(eventType, {
          bubbles: true,
          cancelable: true,
          view: window
        }));
      });
      
      await new Promise(resolve => setTimeout(resolve, 400));
      
      // 再次检查下拉框是否已关闭
      if (!isElementVisible(overlay)) {
        console.log(`[填充调试] "${fieldLabel}" 方法2成功，下拉框已关闭`);
        return true;
      }
    } catch (e) {
      console.log(`[填充调试] "${fieldLabel}" 方法2遇到错误:`, e.message);
    }
    
    // 方法3：尝试双击
    console.log(`[填充调试] "${fieldLabel}" 尝试方法3: 双击`);
    try {
      bestMatch.dispatchEvent(new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window
      }));
      
      await new Promise(resolve => setTimeout(resolve, 400));
    } catch (e) {
      console.log(`[填充调试] "${fieldLabel}" 方法3遇到错误:`, e.message);
    }
    
    // 如果下拉框仍然可见，手动关闭它并标记为部分成功
    if (isElementVisible(overlay)) {
      console.log(`[填充调试] "${fieldLabel}" 手动关闭下拉框`);
      document.body.click();
      return true; // 虽然可能没有完全成功，但我们继续执行下一步
    }
    
    return true;
  }
  
  // 辅助函数：直接选择策略（当搜索策略失败时）
  async function directSelectionStrategy(overlay, value, select, fieldLabel) {
    console.log(`[填充调试] "${fieldLabel}" 开始直接选择策略`);
    
    // 如果找不到覆盖层，重新尝试打开下拉菜单
    if (!overlay) {
      select.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      overlay = await findActiveOverlay();
      
      if (!overlay) {
        console.log(`[填充调试] "${fieldLabel}" 无法找到下拉菜单覆盖层`);
        return { success: false, reason: '无法找到下拉菜单覆盖层' };
      }
    }
    
    // 查找所有可能的选项容器
    const optionsContentContainer = overlay.querySelector('.options-content') ||
                                   overlay.querySelector('[class*="content"]');
    
    // 选项应该在哪个容器中
    const container = optionsContentContainer || overlay;
    
    // 查找所有选项
    const options = container.querySelectorAll('div, span, li');
    const validOptions = Array.from(options).filter(option => {
      // 排除搜索框相关元素
      if (option.querySelector('input') || option.closest('.options-search')) {
        return false;
      }
      
      // 排除空元素和不可见元素
      const text = option.textContent.trim();
      return text && text.length > 0 && isElementVisible(option);
    });
    
    console.log(`[填充调试] "${fieldLabel}" 找到有效选项数量: ${validOptions.length}`);
    
    if (validOptions.length === 0) {
      console.log(`[填充调试] "${fieldLabel}" 未找到有效选项`);
      document.body.click(); // 关闭下拉菜单
      return { success: false, reason: '未找到有效选项' };
    }
    
    // 尝试找到匹配的选项
    let targetOption = validOptions[0]; // 默认使用第一个
    
    // 寻找包含目标值的选项
    for (const option of validOptions) {
      if (option.textContent.includes(value) || value.includes(option.textContent)) {
        targetOption = option;
        console.log(`[填充调试] "${fieldLabel}" 找到匹配选项: ${option.textContent}`);
        break;
      }
    }
    
    // 将选项滚动到视图中
    if (targetOption.scrollIntoView) {
      targetOption.scrollIntoView({ block: 'center' });
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    // 点击选项
    console.log(`[填充调试] "${fieldLabel}" 点击选项: ${targetOption.textContent}`);
    targetOption.click();
    
    // 等待选择生效
    await new Promise(resolve => setTimeout(resolve, 600));
    
    // 即使无法验证选择是否成功，也继续流程
    return { success: true };
  }
  
  // 填充材质成分
  async function fillMaterialField(fieldElement, value) {
    try {
      // 解析材质和百分比，例如：'其他材质100%'
      const match = value.match(/(.+?)(\d+)%/);
      let material = value;
      let percentage = '';
      
      if (match) {
        material = match[1].trim();
        percentage = match[2];
      }
      
      // 填充材质下拉菜单
      const materialSelect = fieldElement.querySelector('.material-item .next-select');
      if (!materialSelect) {
        return { success: false, reason: '未找到材质下拉菜单' };
      }
      
      // 检查当前材质是否已经选中了正确的值
      const currentMaterial = materialSelect.querySelector('em');
      if (!(currentMaterial && currentMaterial.textContent.trim() === material)) {
        materialSelect.click();
        
        // 等待下拉菜单弹出
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 查找菜单容器
        const menuContainer = document.querySelector('.next-overlay-wrapper');
        if (!menuContainer) {
          document.body.click();
          return { success: false, reason: '材质下拉菜单容器未找到' };
        }
        
        // 查找菜单项
        const menuItems = menuContainer.querySelectorAll('.next-menu-item');
        
        // 尝试找到匹配项
        let targetItem = null;
          for (const item of menuItems) {
            const itemText = item.textContent.trim();
          if (itemText === material || itemText.includes(material) || material.includes(itemText)) {
              targetItem = item;
              break;
          }
        }
        
        if (targetItem) {
          targetItem.click();
          await new Promise(resolve => setTimeout(resolve, 200));
        } else {
          document.body.click();
          return { success: false, reason: `未找到匹配的材质选项: ${material}` };
        }
      }
      
      // 填充百分比输入框
      if (percentage) {
        const percentInput = fieldElement.querySelector('.count-number input');
        if (percentInput) {
          percentInput.value = percentage;
          percentInput.dispatchEvent(new Event('input', { bubbles: true }));
          percentInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }
      
      return { success: true };
    } catch (error) {
      return { success: false, reason: error.message };
    }
  }

  // 填充带单位的数值
  async function fillMeasurementField(fieldElement, value) {
    try {
      console.log(`[填充调试] 尝试填充带单位的数值: "${value}"`);
      
      const numberMatch = value.match(/(\d+\.?\d*)/);
      if (!numberMatch) {
        console.error('[填充调试] 无法提取数值');
        return { success: false, reason: '无法提取数值' };
      }
      
      const numberValue = numberMatch[1];
      console.log(`[填充调试] 提取的数值: ${numberValue}`);
      
      const input = fieldElement.querySelector('.sell-o-measurement input');
      
      if (!input) {
        console.error('[填充调试] 未找到数值输入框');
        return { success: false, reason: '未找到数值输入框' };
      }
      
      console.log(`[填充调试] 找到数值输入框，填充值 ${numberValue}`);
      
      input.value = numberValue;
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('[填充调试] 数值输入框填充完成');
      return { success: true };
    } catch (error) {
      console.error('[填充调试] 数值输入框填充出错:', error.message);
      return { success: false, reason: error.message };
    }
  }

  // 填充输入框
  async function fillInputField(fieldElement, value) {
    try {
      console.log(`[填充调试] 尝试填充输入框值: "${value}"`);
      
      const input = fieldElement.querySelector('.next-input input') || 
                   fieldElement.querySelector('input[type="text"]') ||
                   fieldElement.querySelector('input[uitype="input"]') ||
                    fieldElement.querySelector('input');
      
      if (!input) {
        console.error('[填充调试] 未找到输入框');
        return { success: false, reason: '未找到输入框' };
      }
      
      console.log('[填充调试] 找到输入框:', input);
      
      input.value = value;
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('[填充调试] 输入框填充完成');
      return { success: true };
    } catch (error) {
      console.error('[填充调试] 输入框填充出错:', error.message);
      return { success: false, reason: error.message };
    }
  }

  // 直接填充货号字段
  async function fillItemNumberDirectly(itemNo) {
    try {
      console.log(`[填充调试] 尝试直接填充货号: "${itemNo}"`);
      let huohaoInput = null;
      
      // 方法1: 通过新版页面的ID查找
      const huohaoField = document.querySelector('#sell-field-p-13021751');
      if (huohaoField) {
        console.log('[填充调试] 通过ID找到货号字段');
        huohaoInput = huohaoField.querySelector('input');
      }
      
      // 方法2: 通过标签查找
      if (!huohaoInput) {
        console.log('[填充调试] 尝试通过标签查找货号字段');
        const labels = Array.from(document.querySelectorAll('.sell-component-info-wrapper-label, .label, .next-form-item-label, .sell-o-label'));
      const huohaoLabel = labels.find(label => {
        const text = label.textContent.trim();
        return text.includes('货号') || text.includes('商品编号');
      });
      
      if (huohaoLabel) {
          console.log('[填充调试] 找到货号标签:', huohaoLabel.textContent);
          const parent = huohaoLabel.closest('.sell-component-info-wrapper-wrap') || 
                        huohaoLabel.closest('.sell-component-item-prop-item') ||
                        huohaoLabel.closest('.sell-catProp-item') || 
                      huohaoLabel.closest('.next-form-item') || 
                      huohaoLabel.parentElement;
        
        if (parent) {
            huohaoInput = parent.querySelector('input');
            console.log('[填充调试] 通过父元素找到货号输入框');
          }
        }
      }
      
      // 方法3: 直接在所有输入框中查找
      if (!huohaoInput) {
        console.log('[填充调试] 尝试在所有输入框中查找货号字段');
        const allInputs = Array.from(document.querySelectorAll('input'));
        huohaoInput = allInputs.find(input => {
          const placeholder = input.getAttribute('placeholder');
          const name = input.getAttribute('name');
          const parentText = input.parentElement && input.parentElement.textContent;
          
          return (placeholder && placeholder.includes('货号')) || 
                 (name && name.includes('13021751')) ||
                 (parentText && (parentText.includes('货号') || parentText.includes('商品编号')));
        });
        
        if (huohaoInput) {
          console.log('[填充调试] 在所有输入框中找到货号输入框');
        }
      }
      
      if (huohaoInput) {
        console.log('[填充调试] 找到货号输入框，填充值:', itemNo);
        huohaoInput.value = itemNo;
        huohaoInput.dispatchEvent(new Event('input', { bubbles: true }));
        huohaoInput.dispatchEvent(new Event('change', { bubbles: true }));
        
        const success = huohaoInput.value === itemNo;
        console.log('[填充调试] 货号填充' + (success ? '成功' : '失败'));
        return success;
      } else {
        console.error('[填充调试] 未找到货号输入框');
        return false;
      }
    } catch (error) {
      console.error('[填充调试] 货号填充出错:', error.message);
      return false;
    }
  }
  
  // 显示提示
  function showToast(message) {
    const existingToast = document.querySelector('.tb-attribute-toast');
    if (existingToast) {
      existingToast.remove();
    }
    
    const toast = document.createElement('div');
    toast.className = 'tb-attribute-toast';
    toast.style.cssText = 'position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10000000; font-size: 14px;';
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // 将消息显示时间延长到5秒，确保用户能看清信息
    setTimeout(() => {
      toast.remove();
    }, 5000);
  }
})(); 