// 商品详情页处理
class ItemManager {
    constructor() {
        this.initialized = false;
        this.maxRetries = 5;  // 最大重试次数
        this.retryInterval = 1000;  // 重试间隔
        this.messageHandlers = new Map();  // 存储消息处理器
        this.operationHistory = [];  // 仅在内存中保存记录
        
        if (document.readyState === 'complete') {
            this.init();
        } else {
            window.addEventListener('load', () => this.init());
        }
    }

    async init() {
        if (this.initialized) return;
        
        try {
            const itemId = this.getItemId();
            if (!itemId) return;

            await this.waitForToolkit();
            this.createControlPanel(itemId);
            this.initialized = true;

        } catch (e) {
            console.error('初始化失败:', e);
        }
    }

    getItemId() {
        const match = location.href.match(/id=(\d+)/);
        return match ? match[1] : null;
    }

    // 使用MutationObserver等待工具栏加载
    async waitForToolkit(retryCount = 0) {
        return new Promise((resolve, reject) => {
            if (retryCount >= this.maxRetries) {
                reject(new Error('等待工具栏超时'));
                return;
            }

            const toolkitList = document.querySelector('.tb-toolkit-list');
            if (toolkitList) {
                resolve(toolkitList);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const toolkitList = document.querySelector('.tb-toolkit-list');
                if (toolkitList) {
                    obs.disconnect();
                    resolve(toolkitList);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // 设置超时
            setTimeout(() => {
                observer.disconnect();
                this.waitForToolkit(retryCount + 1).then(resolve).catch(reject);
            }, this.retryInterval);
        });
    }

    createControlPanel(itemId) {
        const toolkitList = document.querySelector('.tb-toolkit-list');
        if (!toolkitList) return;

        const buttons = [
            {
                text: '下架',
                action: () => this.putInStock(itemId),
                emoji: '⬇️'
            },
            {
                text: '时间',
                action: () => this.showDeliveryDialog(itemId),
                emoji: '⏱️'
            },
            {
                text: '销量',
                action: () => this.showSalesData(itemId),
                emoji: '📊'
            }
        ];

        buttons.forEach(({ text, action, emoji }) => {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'toolkit-item toolkit-item-link';
            itemDiv.setAttribute('data-name', text);
            itemDiv.setAttribute('data-label', text);
            
            itemDiv.innerHTML = `
                <a style="display: flex; flex-direction: column; align-items: center; text-decoration: none; color: #000; font-size: 10px; padding: 8px 6px;">
                    <span style="font-size: 24px; margin-bottom: 4px; line-height: 1;">${emoji}</span>
                    <span style="line-height: 1.2;">${text}</span>
                </a>
            `;

            const link = itemDiv.querySelector('a');
            this.addHoverEffects(link);
            link.onclick = (e) => {
                e.preventDefault();
                action();
            };

            const feedbackItem = toolkitList.querySelector('[data-name="feedback"]');
            if (feedbackItem) {
                toolkitList.insertBefore(itemDiv, feedbackItem);
            } else {
                toolkitList.appendChild(itemDiv);
            }
        });
    }

    addHoverEffects(element) {
        element.onmouseover = () => {
            element.style.color = '#ff4400';
            element.style.background = '#f5f5f5';
        };
        element.onmouseout = () => {
            element.style.color = '#333';
            element.style.background = 'transparent';
        };
    }

    // 创建和管理消息处理器
    createMessageHandler(type, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const handler = (event) => {
                if (event.data?.type === type) {
                    this.removeMessageHandler(type);
                    resolve(event.data);
                }
                // 处理进度消息
                if (event.data?.type === 'operationProgress') {
                    this.updateProgressDisplay(event.data.data);
                }
            };

            // 存储处理器
            this.messageHandlers.set(type, handler);
            window.addEventListener('message', handler);

            // 设置超时
            setTimeout(() => {
                this.removeMessageHandler(type);
                reject(new Error('操作超时，请重试'));
            }, timeout);
        });
    }

    // 移除消息处理器
    removeMessageHandler(type) {
        const handler = this.messageHandlers.get(type);
        if (handler) {
            window.removeEventListener('message', handler);
            this.messageHandlers.delete(type);
        }
    }

    // 打开新标签页并控制焦点
    async openBackgroundTab(url) {
        try {
            // 创建临时链接
            const a = document.createElement('a');
            a.href = url;
            a.target = '_blank';
            a.rel = 'noopener noreferrer';
            a.style.display = 'none';
            document.body.appendChild(a);

            // 模拟Ctrl+点击
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                ctrlKey: true,  // Windows的Ctrl键
                metaKey: true   // Mac的Command键
            });

            // 触发点击事件
            a.dispatchEvent(clickEvent);

            // 清理临时元素
            setTimeout(() => {
                document.body.removeChild(a);
            }, 100);

            return Promise.resolve();
        } catch (e) {
            console.error('打开新标签页失败:', e);
            // 如果模拟点击失败，回退到原来的方式
            const newTab = window.open(url, '_blank', 'noopener,noreferrer');
            window.focus();
            return Promise.resolve(newTab);
        }
    }

    async showSalesData(itemId) {
        let newTab = null;

        try {
            utils.showMessage('正在获取销量数据...', 'info');

            const salesDisplay = this.createSalesDisplay();
            const messagePromise = this.createMessageHandler('salesData', 5000);
            
            // 打开后台标签页
            const url = `https://qn.taobao.com/home.htm/SellManage/on_sale?queryItemId=${itemId}`;
            newTab = await this.openBackgroundTab(url);

            // 等待销量数据
            const { data } = await messagePromise;
            
            if (!data || !data.totalSales || !data.sales30d) {
                throw new Error('获取数据失败');
            }

            this.updateSalesDisplay(salesDisplay, data, itemId);

        } catch (e) {
            console.error('获取销量数据失败:', e);
            utils.showMessage(e.message || '获取销量数据失败，请重试', 'error');
            this.handleSalesError();
        } finally {
            if (newTab && !newTab.closed) {
                newTab.close();
            }
        }
    }

    createSalesDisplay() {
        let salesDisplay = document.querySelector('.tb-helper-sales-display');
        if (!salesDisplay) {
            salesDisplay = document.createElement('div');
            salesDisplay.className = 'tb-helper-sales-display';
            salesDisplay.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                z-index: 9999999;
                font-size: 14px;
                min-width: 200px;
                border: 1px solid #FF4400;
            `;
            document.body.appendChild(salesDisplay);
        }
        return salesDisplay;
    }

    updateSalesDisplay(display, data, itemId) {
        const { totalSales, sales30d } = data;
        display.innerHTML = `
            <div style="font-weight: bold; color: #333; margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                <span>销量数据</span>
                <span style="color: #999; font-size: 12px;">实时</span>
            </div>
            <div style="display: flex; justify-content: space-between; gap: 20px;">
                <div>
                    <div style="font-size: 20px; color: #FF4400; margin-bottom: 4px;">${totalSales}</div>
                    <div style="color: #666; font-size: 12px;">累计销量</div>
                </div>
                <div>
                    <div style="font-size: 20px; color: #FF4400; margin-bottom: 4px;">${sales30d}</div>
                    <div style="color: #666; font-size: 12px;">30日销量</div>
                </div>
            </div>
            <div style="margin-top: 8px; text-align: right;">
                <a href="#" style="color: #666; font-size: 12px; text-decoration: none;" onclick="window.itemManager.showSalesData('${itemId}'); return false;">刷新</a>
            </div>
        `;
    }

    handleSalesError() {
        const salesDisplay = document.querySelector('.tb-helper-sales-display');
        if (salesDisplay) {
            salesDisplay.innerHTML = `
                <div style="color: #ff4d4f;">
                    获取销量数据失败，请重试
                </div>
            `;
        }
    }

    // 添加操作记录
    addOperationRecord(itemId, operation, timeText = null) {
        const record = {
            itemId,
            operation,
            timeText,
            time: new Date().toLocaleTimeString()  // 只显示时间
        };
        
        this.operationHistory.unshift(record);  // 添加到开头
        if (this.operationHistory.length > 10) {  // 只保留最近10条记录
            this.operationHistory.pop();
        }
        
        this.updateHistoryPanel();
    }

    // 创建历史记录面板
    createHistoryPanel() {
        let panel = document.querySelector('.tb-helper-history');
        if (!panel) {
            panel = document.createElement('div');
            panel.className = 'tb-helper-history';
            panel.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                padding: 12px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                z-index: 9999998;
                font-size: 12px;
                width: 180px;
                border: 1px solid #FF4400;
            `;
            document.body.appendChild(panel);
        }
        return panel;
    }

    // 更新历史记录面板
    updateHistoryPanel() {
        const panel = this.createHistoryPanel();
        
        let html = `
            <div style="font-weight: bold; color: #333; margin-bottom: 8px;">
                操作记录
            </div>
        `;
        
        if (this.operationHistory.length === 0) {
            html += `<div style="color: #999; text-align: center; padding: 4px;">暂无记录</div>`;
        } else {
            html += `<div style="display: flex; flex-direction: column; gap: 4px;">`;
            this.operationHistory.forEach(record => {
                const operationText = record.operation === 'delivery' 
                    ? `设置${record.timeText}发货`
                    : '下架商品';
                    
                html += `
                    <div style="padding: 6px; background: #f5f5f5; border-radius: 4px; font-size: 11px;">
                        <div style="color: #666;">ID: ${record.itemId}</div>
                        <div style="display: flex; justify-content: space-between; margin-top: 2px;">
                            <span style="color: #FF4400;">${operationText}</span>
                            <span style="color: #999;">${record.time}</span>
                        </div>
                    </div>
                `;
            });
            html += `</div>`;
        }
        
        panel.innerHTML = html;
    }

    async putInStock(itemId) {
        try {
            const confirmContent = `
                <div style="margin-bottom: 15px;">
                    <div style="color: #333">是否确认将该商品下架？</div>
                </div>
            `;

            if (!await utils.showConfirm('确认下架', confirmContent)) {
                return;
            }

            // 打开新标签页
            const url = `https://qn.taobao.com/home.htm/SellManage/on_sale?queryItemId=${itemId}&action=putoff&current=1&pageSize=20`;
            await this.openBackgroundTab(url);
            
            // 添加操作记录
            this.addOperationRecord(itemId, 'putoff');

        } catch (e) {
            console.error('下架失败:', e);
            utils.showMessage(e.message || '下架失败，请重试', 'error');
        }
    }

    async showDeliveryDialog(itemId) {
        try {
            const dialog = document.createElement('div');
            dialog.className = 'tb-helper-dialog';
            dialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 10000000;
            `;

            const title = document.createElement('h3');
            title.textContent = '设置发货时间';
            title.style.cssText = `
                margin: 0 0 20px 0;
                font-size: 16px;
                color: #333;
            `;

            const options = [
                { text: '24小时内', value: 1, displayText: '24小时' },
                { text: '48小时内', value: 2, displayText: '48小时' },
                { text: '3天内', value: 3, displayText: '3天' },
                { text: '7天内', value: 7, displayText: '7天' },
                { text: '15天内', value: 15, displayText: '15天' }
            ];

            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = 'display: flex; gap: 10px; flex-wrap: wrap;';

            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 9999999;
            `;

            options.forEach(option => {
                const button = this.createDeliveryButton(option, itemId, dialog, overlay);
                buttonContainer.appendChild(button);
            });

            dialog.appendChild(title);
            dialog.appendChild(buttonContainer);

            overlay.onclick = () => {
                dialog.remove();
                overlay.remove();
            };

            document.body.appendChild(overlay);
            document.body.appendChild(dialog);

        } catch (e) {
            console.error('显示对话框失败:', e);
            utils.showMessage('操作失败，请重试', 'error');
        }
    }

    createDeliveryButton(option, itemId, dialog, overlay) {
        const button = document.createElement('button');
        button.textContent = option.text;
        button.className = 'tb-helper-btn';
        button.style.cssText = `
            padding: 5px 15px;
            background: #FF4400;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        `;

        this.addHoverEffects(button);

        button.onclick = async () => {
            try {
                dialog.remove();
                overlay.remove();

                const url = `https://qn.taobao.com/home.htm/SellManage/on_sale?queryItemId=${itemId}&state=days:${option.value}&current=1&pageSize=20`;
                await this.openBackgroundTab(url);
                
                // 添加操作记录，使用displayText
                this.addOperationRecord(itemId, 'delivery', option.displayText);

            } catch (e) {
                console.error('设置发货时间失败:', e);
                utils.showMessage(e.message || '操作失败，请重试', 'error');
            }
        };

        return button;
    }

    // 创建进度显示
    createProgressDisplay() {
        let display = document.querySelector('.tb-helper-progress');
        if (!display) {
            display = document.createElement('div');
            display.className = 'tb-helper-progress';
            display.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                z-index: 9999999;
                font-size: 14px;
                min-width: 200px;
                border: 1px solid #FF4400;
                transition: all 0.3s ease;
            `;
            document.body.appendChild(display);
        }
        return display;
    }

    // 更新进度显示
    updateProgressDisplay(data) {
        const { step, total, message } = data;
        const display = this.createProgressDisplay();
        const percent = Math.round((step / total) * 100);
        
        display.innerHTML = `
            <div style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: bold; color: #333;">操作进度</span>
                <span style="color: #FF4400;">${percent}%</span>
            </div>
            <div style="background: #f5f5f5; height: 4px; border-radius: 2px; overflow: hidden;">
                <div style="background: #FF4400; width: ${percent}%; height: 100%; transition: width 0.3s ease;"></div>
            </div>
            <div style="margin-top: 8px; color: #666; font-size: 12px;">${message}</div>
        `;

        // 如果是最后一步，3秒后淡出
        if (step === total) {
            setTimeout(() => {
                display.style.opacity = '0';
                display.style.transform = 'translateX(20px)';
                setTimeout(() => display.remove(), 300);
            }, 3000);
        }
    }

    // 显示操作结果
    showOperationResult(success, message) {
        const display = document.createElement('div');
        display.className = 'tb-helper-result';
        display.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 9999999;
            font-size: 14px;
            min-width: 200px;
            border: 1px solid ${success ? '#52c41a' : '#ff4d4f'};
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
        `;

        display.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 20px;">${success ? '✅' : '❌'}</span>
                <span style="color: ${success ? '#52c41a' : '#ff4d4f'};">${message}</span>
            </div>
        `;

        document.body.appendChild(display);
        requestAnimationFrame(() => {
            display.style.opacity = '1';
            display.style.transform = 'translateX(0)';
        });

        setTimeout(() => {
            display.style.opacity = '0';
            display.style.transform = 'translateX(20px)';
            setTimeout(() => display.remove(), 300);
        }, 3000);
    }
}

// 创建实例
window.itemManager = new ItemManager();

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('DOM加载完成，开始初始化...');
        
        // 从URL中获取商品ID
        const itemId = new URL(location.href).searchParams.get('id');
        console.log('从URL获取商品ID:', itemId);
        
        if (!itemId) {
            console.error('未找到商品ID');
            return;
        }

        // 创建操作面板
        const panel = document.createElement('div');
        panel.className = 'tb-helper-container';
        panel.innerHTML = `
            <div class="tb-helper-title">商品管理</div>
            <button class="tb-helper-btn" id="setDeliveryBtn">设置发货时间</button>
        `;
        document.body.appendChild(panel);

        // 绑定点击事件
        const btn = document.getElementById('setDeliveryBtn');
        if (btn) {
            btn.onclick = () => {
                console.log('点击设置发货时间按钮');
                showDeliveryDialog(itemId);
            };
            console.log('已绑定按钮点击事件');
        } else {
            console.error('未找到设置按钮');
        }
        
        console.log('初始化完成');
    } catch (e) {
        console.error('初始化失败:', e);
    }
}); 