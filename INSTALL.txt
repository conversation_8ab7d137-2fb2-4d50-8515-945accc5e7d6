# 淘宝商品属性助手安装和使用指南

## 安装步骤

1. 解压下载的压缩包到一个固定的位置（例如：D:\Extensions\淘宝商品属性助手）
2. 打开Chrome浏览器，在地址栏输入：chrome://extensions/
3. 在右上角找到并开启"开发者模式"
4. 点击左上角的"加载已解压的扩展程序"按钮
5. 选择刚才解压的文件夹
6. 安装成功后，可以在浏览器右上角看到扩展图标

## 常见问题解决

### 按钮不显示
- 确保您已经刷新了页面
- 检查是否在正确的页面（淘宝/天猫商品详情页或淘宝商品编辑页）
- 尝试按F12打开开发者工具，查看控制台是否有错误信息
- 重新启动浏览器后重试

### 属性提取失败
- 确保页面上有完整的商品属性信息
- 对于不同版本的淘宝/天猫页面，可能需要多次点击才能成功
- 如果仍然无法提取，可以尝试刷新页面后重试

### 属性填充失败
- 检查提取的属性格式是否正确
- 对于下拉菜单，查看调试面板了解具体失败原因
- 一些特殊格式的属性（如材质成分）可能需要手动调整
- 如果只有个别属性填充失败，可以手动填写这些字段

### 下拉菜单填充问题
- 扩展会使用多种匹配策略尝试找到正确的选项
- 调试面板会显示所有可选项和匹配过程
- 如果自动匹配失败，可以根据调试面板中显示的可选项手动选择
- 对于特殊格式的选项，如年份季节组合，扩展有专门的处理逻辑

### 扩展被禁用
- 确保您的Chrome浏览器允许开发者模式的扩展运行
- 在chrome://extensions/页面检查扩展是否处于启用状态
- 如果扩展被自动禁用，需要重新启用

## 使用技巧

1. **剪贴板功能**：提取到属性后，可以点击"复制到剪贴板"按钮，将属性数据保存起来，方便在不同页面使用

2. **调试模式**：填充过程中自动显示的调试面板可以帮助您了解填充失败的原因，便于手动处理

3. **特殊属性处理**：
   - 货号/商品编号会优先填充
   - 材质成分会自动解析为材质和百分比
   - 带单位的数值会自动提取数值部分

4. **批量操作**：可以在多个商品页面之间切换，提取一个产品的属性后填充到另一个产品编辑页

## 更新方法

当有新版本发布时，请按照以下步骤更新：

1. 在chrome://extensions/页面找到"淘宝商品属性助手"
2. 点击"删除"按钮卸载旧版本
3. 解压新版本文件到固定位置
4. 按照安装步骤重新安装

## 隐私和权限说明

本扩展仅在以下域名下工作：
- *.taobao.com
- item.taobao.com
- sell.taobao.com
- detail.tmall.com

扩展仅读取页面上的商品属性信息，不会收集或发送您的个人数据。

所需权限说明：
- storage: 用于存储提取的属性数据
- activeTab: 用于读取和修改当前标签页内容
- scripting: 用于执行填充脚本
- tabs: 用于检测当前标签页URL

## 联系方式

如有任何问题或建议，请联系开发者。 