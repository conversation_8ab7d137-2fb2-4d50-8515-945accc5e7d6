// 通用工具函数
const utils = {
    // 从URL获取商品ID
    getItemId() {
        try {
            return new URL(location.href).searchParams.get('id');
        } catch (e) {
            console.error('获取商品ID失败:', e);
            return null;
        }
    },

    // 从页面获取token
    getTbToken() {
        const tokenMatch = document.cookie.match(/_tb_token_=([^;]+)/);
        return tokenMatch ? tokenMatch[1] : null;
    },

    // 显示消息提示
    showMessage(message, type = 'info') {
        // 移除已有的消息
        const existingMessages = document.querySelectorAll('.tb-helper-message');
        existingMessages.forEach(msg => {
            if (msg.classList.contains('tb-helper-message-fade')) {
                msg.remove();
            }
        });

        // 创建新消息
        const div = document.createElement('div');
        div.className = `tb-helper-message tb-helper-message-${type}`;
        div.textContent = message;
        
        // 添加图标
        const icon = document.createElement('i');
        icon.className = `tb-helper-icon tb-helper-icon-${type}`;
        div.insertBefore(icon, div.firstChild);
        
        // 添加关闭按钮
        const closeBtn = document.createElement('span');
        closeBtn.className = 'tb-helper-message-close';
        closeBtn.innerHTML = '×';
        closeBtn.onclick = () => div.remove();
        div.appendChild(closeBtn);
        
        document.body.appendChild(div);
        
        // 2秒后自动消失
        setTimeout(() => {
            div.classList.add('tb-helper-message-fade');
            setTimeout(() => div.remove(), 500);
        }, 2000);
    },

    // 显示确认对话框
    showConfirm(title, content) {
        return new Promise((resolve) => {
            // 移除已有的对话框
            const existingModal = document.querySelector('.tb-helper-modal');
            if (existingModal) {
                existingModal.remove();
            }

            // 创建遮罩层
            const modal = document.createElement('div');
            modal.className = 'tb-helper-modal';
            
            // 创建对话框内容
            modal.innerHTML = `
                <div class="tb-helper-modal-content">
                    <div class="tb-helper-modal-header">
                        <h3>${title}</h3>
                        <span class="tb-helper-modal-close">×</span>
                    </div>
                    <div class="tb-helper-modal-body">
                        <p>${content}</p>
                    </div>
                    <div class="tb-helper-modal-footer">
                        <button class="tb-helper-btn tb-helper-btn-cancel">取消</button>
                        <button class="tb-helper-btn tb-helper-btn-confirm">确定</button>
                    </div>
                </div>
            `;

            // 绑定事件
            const handleClose = (result) => {
                modal.classList.add('tb-helper-modal-fade');
                setTimeout(() => modal.remove(), 300);
                resolve(result);
            };

            // 关闭按钮
            modal.querySelector('.tb-helper-modal-close').onclick = () => handleClose(false);
            
            // 取消按钮
            modal.querySelector('.tb-helper-btn-cancel').onclick = () => handleClose(false);
            
            // 确认按钮
            modal.querySelector('.tb-helper-btn-confirm').onclick = () => handleClose(true);
            
            // ESC键关闭
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    handleClose(false);
                    document.removeEventListener('keydown', handleKeyDown);
                }
            };
            document.addEventListener('keydown', handleKeyDown);

            // 点击遮罩层关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    handleClose(false);
                }
            };

            document.body.appendChild(modal);
            
            // 聚焦确认按钮
            setTimeout(() => {
                modal.querySelector('.tb-helper-btn-confirm').focus();
            }, 100);
        });
    },

    // 显示发货时间选择对话框
    showDeliveryDialog() {
        return new Promise((resolve) => {
            // 移除已有的对话框
            const existingModal = document.querySelector('.tb-helper-modal');
            if (existingModal) {
                existingModal.remove();
            }

            // 创建对话框
            const modal = document.createElement('div');
            modal.className = 'tb-helper-modal';
            
            // 创建对话框内容
            modal.innerHTML = `
                <div class="tb-helper-modal-content">
                    <div class="tb-helper-modal-header">
                        <h3>设置发货时间</h3>
                        <span class="tb-helper-modal-close">×</span>
                    </div>
                    <div class="tb-helper-modal-body">
                        <div class="tb-helper-delivery-options">
                            <button class="tb-helper-btn" data-days="1">24小时内</button>
                            <button class="tb-helper-btn" data-days="2">48小时内</button>
                            <button class="tb-helper-btn" data-days="3">3天内</button>
                            <button class="tb-helper-btn" data-days="5">5天内</button>
                            <button class="tb-helper-btn" data-days="7">7天内</button>
                            <div class="tb-helper-custom-days">
                                <input type="number" min="3" max="30" placeholder="自定义天数(3-30)">
                                <button class="tb-helper-btn">确定</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 处理关闭
            const handleClose = (days = null) => {
                modal.classList.add('tb-helper-modal-fade');
                setTimeout(() => modal.remove(), 300);
                resolve(days);
            };

            // 关闭按钮
            modal.querySelector('.tb-helper-modal-close').onclick = () => handleClose();

            // 快捷选项按钮
            modal.querySelectorAll('[data-days]').forEach(btn => {
                btn.onclick = () => handleClose(parseInt(btn.dataset.days));
            });

            // 自定义天数
            const customInput = modal.querySelector('input');
            const customBtn = modal.querySelector('.tb-helper-custom-days button');
            
            customBtn.onclick = () => {
                const days = parseInt(customInput.value);
                if (days >= 3 && days <= 30) {
                    handleClose(days);
                } else {
                    this.showMessage('请输入3-30之间的天数', 'error');
                }
            };

            // ESC键关闭
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    handleClose();
                    document.removeEventListener('keydown', handleKeyDown);
                }
            };
            document.addEventListener('keydown', handleKeyDown);

            // 点击遮罩层关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    handleClose();
                }
            };

            document.body.appendChild(modal);
            
            // 聚焦输入框
            setTimeout(() => {
                customInput.focus();
            }, 100);
        });
    },

    // 防抖函数
    debounce(fn, delay = 300) {
        let timer = null;
        return function (...args) {
            if (timer) clearTimeout(timer);
            timer = setTimeout(() => {
                fn.apply(this, args);
                timer = null;
            }, delay);
        };
    },

    // 节流函数
    throttle(fn, delay = 300) {
        let timer = null;
        return function (...args) {
            if (timer) return;
            timer = setTimeout(() => {
                fn.apply(this, args);
                timer = null;
            }, delay);
        };
    },

    // 等待元素出现
    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) return element;
            await new Promise(r => setTimeout(r, 100));
        }
        return null;
    },

    // 延迟函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    // 检查是否是卖家
    isSeller() {
        return window.g_config?.isSeller === true;
    },

    // 格式化日期
    formatDate(date = new Date()) {
        const pad = num => String(num).padStart(2, '0');
        return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
    },

    // 记录日志
    log(type, message, data = null) {
        const time = this.formatDate();
        console.log(`[${time}] [${type}] ${message}`, data || '');
    },

    // 请求频率控制
    rateLimiter: {
        lastRequest: 0,
        minInterval: 1000, // 最小间隔1秒

        async execute(fn) {
            const now = Date.now();
            const timeSinceLastRequest = now - this.lastRequest;
            
            if (timeSinceLastRequest < this.minInterval) {
                await new Promise(r => setTimeout(r, this.minInterval - timeSinceLastRequest));
            }
            
            this.lastRequest = Date.now();
            return fn();
        }
    },

    // 带重试机制的请求包装
    async requestWithRetry(requestFn, maxRetries = 3) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await this.rateLimiter.execute(requestFn);
            } catch (e) {
                if (i === maxRetries - 1) throw e;
                await new Promise(r => setTimeout(r, 1000 * (i + 1))); // 延迟重试
                console.log(`重试第${i + 1}次...`);
            }
        }
    }
};

// 导出工具函数
window.utils = utils; 